/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/dashboard/page";
exports.ids = ["app/dashboard/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5CDubyRodman%5CDocuments%5Caugment-projects%5CClipsnip_ai%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDubyRodman%5CDocuments%5Caugment-projects%5CClipsnip_ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5CDubyRodman%5CDocuments%5Caugment-projects%5CClipsnip_ai%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDubyRodman%5CDocuments%5Caugment-projects%5CClipsnip_ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9100\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'dashboard',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/dashboard/page.tsx */ \"(rsc)/./app/dashboard/page.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/dashboard/layout.tsx */ \"(rsc)/./app/dashboard/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\layout.tsx\"],\n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/dashboard/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/dashboard/page\",\n        pathname: \"/dashboard\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5CDubyRodman%5CDocuments%5Caugment-projects%5CClipsnip_ai%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDubyRodman%5CDocuments%5Caugment-projects%5CClipsnip_ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDubyRodman%5C%5CDocuments%5C%5Caugment-projects%5C%5CClipsnip_ai%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDubyRodman%5C%5CDocuments%5C%5Caugment-projects%5C%5CClipsnip_ai%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/dashboard/page.tsx */ \"(ssr)/./app/dashboard/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0R1YnlSb2RtYW4lNUMlNUNEb2N1bWVudHMlNUMlNUNhdWdtZW50LXByb2plY3RzJTVDJTVDQ2xpcHNuaXBfYWklNUMlNUNhcHAlNUMlNUNkYXNoYm9hcmQlNUMlNUNwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsNEpBQThIIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbXktdjAtcHJvamVjdC8/YzBmMyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkM6XFxcXFVzZXJzXFxcXER1YnlSb2RtYW5cXFxcRG9jdW1lbnRzXFxcXGF1Z21lbnQtcHJvamVjdHNcXFxcQ2xpcHNuaXBfYWlcXFxcYXBwXFxcXGRhc2hib2FyZFxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDubyRodman%5C%5CDocuments%5C%5Caugment-projects%5C%5CClipsnip_ai%5C%5Capp%5C%5Cdashboard%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDubyRodman%5C%5CDocuments%5C%5Caugment-projects%5C%5CClipsnip_ai%5C%5Ccomponents%5C%5Cdashboard%5C%5Cdashboard-header.tsx%22%2C%22ids%22%3A%5B%22DashboardHeader%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDubyRodman%5C%5CDocuments%5C%5Caugment-projects%5C%5CClipsnip_ai%5C%5Ccomponents%5C%5Cdashboard%5C%5Cdashboard-nav.tsx%22%2C%22ids%22%3A%5B%22DashboardNav%22%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDubyRodman%5C%5CDocuments%5C%5Caugment-projects%5C%5CClipsnip_ai%5C%5Ccomponents%5C%5Cdashboard%5C%5Cdashboard-header.tsx%22%2C%22ids%22%3A%5B%22DashboardHeader%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDubyRodman%5C%5CDocuments%5C%5Caugment-projects%5C%5CClipsnip_ai%5C%5Ccomponents%5C%5Cdashboard%5C%5Cdashboard-nav.tsx%22%2C%22ids%22%3A%5B%22DashboardNav%22%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/dashboard/dashboard-header.tsx */ \"(ssr)/./components/dashboard/dashboard-header.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/dashboard/dashboard-nav.tsx */ \"(ssr)/./components/dashboard/dashboard-nav.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0R1YnlSb2RtYW4lNUMlNUNEb2N1bWVudHMlNUMlNUNhdWdtZW50LXByb2plY3RzJTVDJTVDQ2xpcHNuaXBfYWklNUMlNUNjb21wb25lbnRzJTVDJTVDZGFzaGJvYXJkJTVDJTVDZGFzaGJvYXJkLWhlYWRlci50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJEYXNoYm9hcmRIZWFkZXIlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDRHVieVJvZG1hbiU1QyU1Q0RvY3VtZW50cyU1QyU1Q2F1Z21lbnQtcHJvamVjdHMlNUMlNUNDbGlwc25pcF9haSU1QyU1Q2NvbXBvbmVudHMlNUMlNUNkYXNoYm9hcmQlNUMlNUNkYXNoYm9hcmQtbmF2LnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMkRhc2hib2FyZE5hdiUyMiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsa01BQXNMO0FBQ3RMO0FBQ0EsNExBQWdMIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbXktdjAtcHJvamVjdC8/OGNiYyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIkRhc2hib2FyZEhlYWRlclwiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXER1YnlSb2RtYW5cXFxcRG9jdW1lbnRzXFxcXGF1Z21lbnQtcHJvamVjdHNcXFxcQ2xpcHNuaXBfYWlcXFxcY29tcG9uZW50c1xcXFxkYXNoYm9hcmRcXFxcZGFzaGJvYXJkLWhlYWRlci50c3hcIik7XG47XG5pbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIkRhc2hib2FyZE5hdlwiXSAqLyBcIkM6XFxcXFVzZXJzXFxcXER1YnlSb2RtYW5cXFxcRG9jdW1lbnRzXFxcXGF1Z21lbnQtcHJvamVjdHNcXFxcQ2xpcHNuaXBfYWlcXFxcY29tcG9uZW50c1xcXFxkYXNoYm9hcmRcXFxcZGFzaGJvYXJkLW5hdi50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDubyRodman%5C%5CDocuments%5C%5Caugment-projects%5C%5CClipsnip_ai%5C%5Ccomponents%5C%5Cdashboard%5C%5Cdashboard-header.tsx%22%2C%22ids%22%3A%5B%22DashboardHeader%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDubyRodman%5C%5CDocuments%5C%5Caugment-projects%5C%5CClipsnip_ai%5C%5Ccomponents%5C%5Cdashboard%5C%5Cdashboard-nav.tsx%22%2C%22ids%22%3A%5B%22DashboardNav%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDubyRodman%5C%5CDocuments%5C%5Caugment-projects%5C%5CClipsnip_ai%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDubyRodman%5C%5CDocuments%5C%5Caugment-projects%5C%5CClipsnip_ai%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDubyRodman%5C%5CDocuments%5C%5Caugment-projects%5C%5CClipsnip_ai%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDubyRodman%5C%5CDocuments%5C%5Caugment-projects%5C%5CClipsnip_ai%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDubyRodman%5C%5CDocuments%5C%5Caugment-projects%5C%5CClipsnip_ai%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDubyRodman%5C%5CDocuments%5C%5Caugment-projects%5C%5CClipsnip_ai%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/theme-provider.tsx */ \"(ssr)/./components/theme-provider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0R1YnlSb2RtYW4lNUMlNUNEb2N1bWVudHMlNUMlNUNhdWdtZW50LXByb2plY3RzJTVDJTVDQ2xpcHNuaXBfYWklNUMlNUNjb21wb25lbnRzJTVDJTVDdGhlbWUtcHJvdmlkZXIudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTIyVGhlbWVQcm92aWRlciUyMiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJDJTNBJTVDJTVDVXNlcnMlNUMlNUNEdWJ5Um9kbWFuJTVDJTVDRG9jdW1lbnRzJTVDJTVDYXVnbWVudC1wcm9qZWN0cyU1QyU1Q0NsaXBzbmlwX2FpJTVDJTVDbm9kZV9tb2R1bGVzJTVDJTVDbmV4dCU1QyU1Q2ZvbnQlNUMlNUNnb29nbGUlNUMlNUN0YXJnZXQuY3NzJTNGJTdCJTVDJTIycGF0aCU1QyUyMiUzQSU1QyUyMmFwcCU1QyU1QyU1QyU1Q2xheW91dC50c3glNUMlMjIlMkMlNUMlMjJpbXBvcnQlNUMlMjIlM0ElNUMlMjJJbnRlciU1QyUyMiUyQyU1QyUyMmFyZ3VtZW50cyU1QyUyMiUzQSU1QiU3QiU1QyUyMnN1YnNldHMlNUMlMjIlM0ElNUIlNUMlMjJsYXRpbiU1QyUyMiU1RCU3RCU1RCUyQyU1QyUyMnZhcmlhYmxlTmFtZSU1QyUyMiUzQSU1QyUyMmludGVyJTVDJTIyJTdEJTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q0R1YnlSb2RtYW4lNUMlNUNEb2N1bWVudHMlNUMlNUNhdWdtZW50LXByb2plY3RzJTVDJTVDQ2xpcHNuaXBfYWklNUMlNUNhcHAlNUMlNUNnbG9iYWxzLmNzcyUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsMEtBQXVLIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbXktdjAtcHJvamVjdC8/YTE3YyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcIlRoZW1lUHJvdmlkZXJcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxEdWJ5Um9kbWFuXFxcXERvY3VtZW50c1xcXFxhdWdtZW50LXByb2plY3RzXFxcXENsaXBzbmlwX2FpXFxcXGNvbXBvbmVudHNcXFxcdGhlbWUtcHJvdmlkZXIudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDubyRodman%5C%5CDocuments%5C%5Caugment-projects%5C%5CClipsnip_ai%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDubyRodman%5C%5CDocuments%5C%5Caugment-projects%5C%5CClipsnip_ai%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDubyRodman%5C%5CDocuments%5C%5Caugment-projects%5C%5CClipsnip_ai%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDubyRodman%5C%5CDocuments%5C%5Caugment-projects%5C%5CClipsnip_ai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDubyRodman%5C%5CDocuments%5C%5Caugment-projects%5C%5CClipsnip_ai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDubyRodman%5C%5CDocuments%5C%5Caugment-projects%5C%5CClipsnip_ai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDubyRodman%5C%5CDocuments%5C%5Caugment-projects%5C%5CClipsnip_ai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDubyRodman%5C%5CDocuments%5C%5Caugment-projects%5C%5CClipsnip_ai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDubyRodman%5C%5CDocuments%5C%5Caugment-projects%5C%5CClipsnip_ai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDubyRodman%5C%5CDocuments%5C%5Caugment-projects%5C%5CClipsnip_ai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDubyRodman%5C%5CDocuments%5C%5Caugment-projects%5C%5CClipsnip_ai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDubyRodman%5C%5CDocuments%5C%5Caugment-projects%5C%5CClipsnip_ai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDubyRodman%5C%5CDocuments%5C%5Caugment-projects%5C%5CClipsnip_ai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDubyRodman%5C%5CDocuments%5C%5Caugment-projects%5C%5CClipsnip_ai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDubyRodman%5C%5CDocuments%5C%5Caugment-projects%5C%5CClipsnip_ai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDubyRodman%5C%5CDocuments%5C%5Caugment-projects%5C%5CClipsnip_ai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDubyRodman%5C%5CDocuments%5C%5Caugment-projects%5C%5CClipsnip_ai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDubyRodman%5C%5CDocuments%5C%5Caugment-projects%5C%5CClipsnip_ai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDubyRodman%5C%5CDocuments%5C%5Caugment-projects%5C%5CClipsnip_ai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDubyRodman%5C%5CDocuments%5C%5Caugment-projects%5C%5CClipsnip_ai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5CDubyRodman%5C%5CDocuments%5C%5Caugment-projects%5C%5CClipsnip_ai%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/dashboard/page.tsx":
/*!********************************!*\
  !*** ./app/dashboard/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/input */ \"(ssr)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_switch__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/switch */ \"(ssr)/./components/ui/switch.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/label */ \"(ssr)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_slider__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/slider */ \"(ssr)/./components/ui/slider.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(ssr)/./components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_Clock_Copy_Download_Info_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,Clock,Copy,Download,Info,Upload!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_Clock_Copy_Download_Info_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,Clock,Copy,Download,Info,Upload!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_Clock_Copy_Download_Info_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,Clock,Copy,Download,Info,Upload!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-down.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_Clock_Copy_Download_Info_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,Clock,Copy,Download,Info,Upload!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/info.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_Clock_Copy_Download_Info_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,Clock,Copy,Download,Info,Upload!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_Clock_Copy_Download_Info_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,Clock,Copy,Download,Info,Upload!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronDown_Clock_Copy_Download_Info_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronDown,Clock,Copy,Download,Info,Upload!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/copy.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! framer-motion */ \"(ssr)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_tooltip__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/tooltip */ \"(ssr)/./components/ui/tooltip.tsx\");\n/* harmony import */ var _lib_store__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/lib/store */ \"(ssr)/./lib/store.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction DashboardPage() {\n    const { user } = (0,_lib_store__WEBPACK_IMPORTED_MODULE_11__.useUserStore)();\n    const [step, setStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"input\");\n    const [videoUrl, setVideoUrl] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [clipLength, setClipLength] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(30);\n    const [subtitleColor, setSubtitleColor] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"#FFFFFF\");\n    const [subtitleSize, setSubtitleSize] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(16);\n    const [subtitleBackground, setSubtitleBackground] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [smartCrop, setSmartCrop] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isProcessing, setIsProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [clipsRemaining, setClipsRemaining] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(user?.plan === \"free\" ? 3 : user?.plan === \"pro\" ? 47 : Number.POSITIVE_INFINITY);\n    const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        if (!videoUrl && !fileInputRef.current?.files?.length) return;\n        setIsProcessing(true);\n        setStep(\"processing\");\n        // Simulate processing time\n        setTimeout(()=>{\n            setIsProcessing(false);\n            setStep(\"result\");\n            if (clipsRemaining !== Number.POSITIVE_INFINITY) {\n                setClipsRemaining((prev)=>prev - 1) // Decrease remaining clips\n                ;\n            }\n        }, 3000);\n    };\n    const resetForm = ()=>{\n        setVideoUrl(\"\");\n        setStep(\"input\");\n        if (inputRef.current) {\n            inputRef.current.focus();\n        }\n        if (fileInputRef.current) {\n            fileInputRef.current.value = \"\";\n        }\n    };\n    const handleFileUpload = ()=>{\n        fileInputRef.current?.click();\n    };\n    const handleFileChange = (e)=>{\n        if (e.target.files && e.target.files.length > 0) {\n            // Just set a placeholder for the UI\n            setVideoUrl(e.target.files[0].name);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"max-w-5xl mx-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-luxury-green\",\n                                children: \"Create a Clip\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 78,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600\",\n                                children: \"Turn any video into a viral short in seconds\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 79,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-gradient-to-r from-luxury-gold/10 to-luxury-green/10 px-4 py-2 rounded-xl border border-luxury-gold/20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm font-medium text-luxury-green\",\n                            children: user?.plan === \"free\" ? `${clipsRemaining} free clips remaining today` : user?.plan === \"pro\" ? `${clipsRemaining} clips remaining this month` : \"Unlimited clips available\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 81,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 76,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col lg:flex-row gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full lg:w-2/3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.Card, {\n                            className: \"bg-white rounded-2xl shadow-sm overflow-hidden mb-6 border border-luxury-gold/10\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.CardContent, {\n                                className: \"p-6\",\n                                children: [\n                                    step === \"input\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.form, {\n                                        onSubmit: handleSubmit,\n                                        className: \"w-full\",\n                                        initial: {\n                                            opacity: 0,\n                                            y: 10\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            duration: 0.3\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex gap-2 mb-6\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"relative flex-1\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_3__.Input, {\n                                                            ref: inputRef,\n                                                            type: \"text\",\n                                                            placeholder: \"Paste YouTube, TikTok, or Instagram link…\",\n                                                            className: \"h-14 pl-4 pr-12 rounded-2xl border-2 focus-visible:ring-luxury-green focus-visible:border-luxury-green transition-all shadow-sm hover:shadow-md\",\n                                                            value: videoUrl,\n                                                            onChange: (e)=>setVideoUrl(e.target.value)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 106,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 105,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"file\",\n                                                        ref: fileInputRef,\n                                                        className: \"hidden\",\n                                                        accept: \"video/*\",\n                                                        onChange: handleFileChange\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 115,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        type: \"button\",\n                                                        variant: \"outline\",\n                                                        className: \"h-14 px-4 rounded-2xl border-2 hover:border-luxury-green hover:text-luxury-green transition-all\",\n                                                        onClick: handleFileUpload,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_Clock_Copy_Download_Info_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                            className: \"h-5 w-5\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 128,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 122,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 104,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border border-luxury-gold/20 rounded-xl p-4 mb-6 bg-gradient-to-br from-luxury-gold/5 to-luxury-green/5\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-medium mb-4 text-luxury-green\",\n                                                        children: \"Customize\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 133,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-6\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center justify-between mb-2\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                htmlFor: \"clip-length\",\n                                                                                className: \"text-luxury-green\",\n                                                                                children: [\n                                                                                    \"Clip Length: \",\n                                                                                    clipLength,\n                                                                                    \"s\"\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 137,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_7__.DropdownMenu, {\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_7__.DropdownMenuTrigger, {\n                                                                                        asChild: true,\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                                            variant: \"ghost\",\n                                                                                            size: \"sm\",\n                                                                                            className: \"h-8 rounded-lg text-luxury-green hover:bg-luxury-green/10\",\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_Clock_Copy_Download_Info_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                                                    className: \"mr-1 h-3 w-3\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                                    lineNumber: 147,\n                                                                                                    columnNumber: 33\n                                                                                                }, this),\n                                                                                                \"Presets\",\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_Clock_Copy_Download_Info_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                                                    className: \"ml-1 h-3 w-3\"\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                                    lineNumber: 149,\n                                                                                                    columnNumber: 33\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                            lineNumber: 142,\n                                                                                            columnNumber: 31\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                        lineNumber: 141,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_7__.DropdownMenuContent, {\n                                                                                        align: \"end\",\n                                                                                        className: \"rounded-xl\",\n                                                                                        children: [\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_7__.DropdownMenuItem, {\n                                                                                                onClick: ()=>setClipLength(15),\n                                                                                                children: \"15 seconds (TikTok)\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                                lineNumber: 153,\n                                                                                                columnNumber: 31\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_7__.DropdownMenuItem, {\n                                                                                                onClick: ()=>setClipLength(30),\n                                                                                                children: \"30 seconds (Reels)\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                                lineNumber: 154,\n                                                                                                columnNumber: 31\n                                                                                            }, this),\n                                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_7__.DropdownMenuItem, {\n                                                                                                onClick: ()=>setClipLength(60),\n                                                                                                children: \"60 seconds (YouTube)\"\n                                                                                            }, void 0, false, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                                lineNumber: 155,\n                                                                                                columnNumber: 31\n                                                                                            }, this)\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                        lineNumber: 152,\n                                                                                        columnNumber: 29\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 140,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 136,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_slider__WEBPACK_IMPORTED_MODULE_6__.Slider, {\n                                                                        id: \"clip-length\",\n                                                                        min: 15,\n                                                                        max: 60,\n                                                                        step: 1,\n                                                                        value: [\n                                                                            clipLength\n                                                                        ],\n                                                                        onValueChange: (value)=>setClipLength(value[0]),\n                                                                        className: \"[&_[role=slider]]:h-5 [&_[role=slider]]:w-5 [&_[role=slider]]:border-2 [&_[role=slider]]:border-luxury-green [&_[role=slider]]:bg-white [&_[role=slider]]:shadow-md [&>[data-orientation=horizontal]]:h-2 [&>[data-orientation=horizontal]]:bg-luxury-green/20 [&>[data-orientation=horizontal]_[data-orientation=horizontal]]:bg-luxury-green\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 161,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex justify-between text-xs text-gray-500 mt-1\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"15s\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 171,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"30s\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 172,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"45s\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 173,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                children: \"60s\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 174,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 170,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 135,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                className: \"mb-2 block text-luxury-green\",\n                                                                                children: \"Subtitle Color:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 180,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex gap-2\",\n                                                                                children: [\n                                                                                    [\n                                                                                        \"#FFFFFF\",\n                                                                                        \"#D4AF37\",\n                                                                                        \"#1B4332\",\n                                                                                        \"#000000\",\n                                                                                        \"#FF6B6B\"\n                                                                                    ].map((color)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                            type: \"button\",\n                                                                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.cn)(\"w-8 h-8 rounded-full border-2 transition-all\", subtitleColor === color ? \"border-luxury-green scale-105 shadow-md\" : \"border-gray-200\"),\n                                                                                            style: {\n                                                                                                backgroundColor: color\n                                                                                            },\n                                                                                            onClick: ()=>setSubtitleColor(color),\n                                                                                            disabled: user?.plan === \"free\"\n                                                                                        }, color, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                            lineNumber: 183,\n                                                                                            columnNumber: 31\n                                                                                        }, this)),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                        type: \"color\",\n                                                                                        value: subtitleColor,\n                                                                                        onChange: (e)=>setSubtitleColor(e.target.value),\n                                                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.cn)(\"w-8 h-8 rounded-full overflow-hidden cursor-pointer border-2 border-gray-200\", user?.plan === \"free\" ? \"opacity-50 cursor-not-allowed\" : \"\"),\n                                                                                        disabled: user?.plan === \"free\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                        lineNumber: 197,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    user?.plan === \"free\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_10__.TooltipProvider, {\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_10__.Tooltip, {\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_10__.TooltipTrigger, {\n                                                                                                    asChild: true,\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"flex items-center ml-1\",\n                                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_Clock_Copy_Download_Info_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                                            className: \"h-4 w-4 text-gray-400\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                                            lineNumber: 212,\n                                                                                                            columnNumber: 39\n                                                                                                        }, this)\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                                        lineNumber: 211,\n                                                                                                        columnNumber: 37\n                                                                                                    }, this)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                                    lineNumber: 210,\n                                                                                                    columnNumber: 35\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_10__.TooltipContent, {\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                        children: \"Available in Pro and Clipper plans\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                                        lineNumber: 216,\n                                                                                                        columnNumber: 37\n                                                                                                    }, this)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                                    lineNumber: 215,\n                                                                                                    columnNumber: 35\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                            lineNumber: 209,\n                                                                                            columnNumber: 33\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                        lineNumber: 208,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 181,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 179,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                className: \"mb-2 block text-luxury-green\",\n                                                                                children: \"Subtitle Size:\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 225,\n                                                                                columnNumber: 27\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center gap-3\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_slider__WEBPACK_IMPORTED_MODULE_6__.Slider, {\n                                                                                        min: 12,\n                                                                                        max: 24,\n                                                                                        step: 1,\n                                                                                        value: [\n                                                                                            subtitleSize\n                                                                                        ],\n                                                                                        onValueChange: (value)=>setSubtitleSize(value[0]),\n                                                                                        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.cn)(\"[&_[role=slider]]:h-5 [&_[role=slider]]:w-5 [&_[role=slider]]:border-2 [&_[role=slider]]:border-luxury-green [&_[role=slider]]:bg-white [&_[role=slider]]:shadow-md [&>[data-orientation=horizontal]]:h-2 [&>[data-orientation=horizontal]]:bg-luxury-green/20 [&>[data-orientation=horizontal]_[data-orientation=horizontal]]:bg-luxury-green flex-1\", user?.plan === \"free\" ? \"opacity-50\" : \"\"),\n                                                                                        disabled: user?.plan === \"free\"\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                        lineNumber: 227,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"text-sm w-8 text-luxury-green font-medium\",\n                                                                                        children: [\n                                                                                            subtitleSize,\n                                                                                            \"px\"\n                                                                                        ]\n                                                                                    }, void 0, true, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                        lineNumber: 239,\n                                                                                        columnNumber: 29\n                                                                                    }, this),\n                                                                                    user?.plan === \"free\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_10__.TooltipProvider, {\n                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_10__.Tooltip, {\n                                                                                            children: [\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_10__.TooltipTrigger, {\n                                                                                                    asChild: true,\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                        className: \"flex items-center\",\n                                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_Clock_Copy_Download_Info_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                                            className: \"h-4 w-4 text-gray-400\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                                            lineNumber: 245,\n                                                                                                            columnNumber: 39\n                                                                                                        }, this)\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                                        lineNumber: 244,\n                                                                                                        columnNumber: 37\n                                                                                                    }, this)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                                    lineNumber: 243,\n                                                                                                    columnNumber: 35\n                                                                                                }, this),\n                                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_10__.TooltipContent, {\n                                                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                        children: \"Available in Pro and Clipper plans\"\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                                        lineNumber: 249,\n                                                                                                        columnNumber: 37\n                                                                                                    }, this)\n                                                                                                }, void 0, false, {\n                                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                                    lineNumber: 248,\n                                                                                                    columnNumber: 35\n                                                                                                }, this)\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                            lineNumber: 242,\n                                                                                            columnNumber: 33\n                                                                                        }, this)\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                        lineNumber: 241,\n                                                                                        columnNumber: 31\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                lineNumber: 226,\n                                                                                columnNumber: 27\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 224,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 178,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center justify-between\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                    htmlFor: \"subtitle-background\",\n                                                                                    className: \"cursor-pointer text-luxury-green\",\n                                                                                    children: \"Subtitle Background\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                    lineNumber: 261,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"flex items-center gap-2\",\n                                                                                    children: [\n                                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_4__.Switch, {\n                                                                                            id: \"subtitle-background\",\n                                                                                            checked: subtitleBackground,\n                                                                                            onCheckedChange: setSubtitleBackground,\n                                                                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_8__.cn)(\"data-[state=checked]:bg-luxury-green\", user?.plan === \"free\" ? \"opacity-50\" : \"\"),\n                                                                                            disabled: user?.plan === \"free\"\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                            lineNumber: 265,\n                                                                                            columnNumber: 31\n                                                                                        }, this),\n                                                                                        user?.plan === \"free\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_10__.TooltipProvider, {\n                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_10__.Tooltip, {\n                                                                                                children: [\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_10__.TooltipTrigger, {\n                                                                                                        asChild: true,\n                                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                                            className: \"flex items-center\",\n                                                                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_Clock_Copy_Download_Info_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                                                                className: \"h-4 w-4 text-gray-400\"\n                                                                                                            }, void 0, false, {\n                                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                                                lineNumber: 280,\n                                                                                                                columnNumber: 41\n                                                                                                            }, this)\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                                            lineNumber: 279,\n                                                                                                            columnNumber: 39\n                                                                                                        }, this)\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                                        lineNumber: 278,\n                                                                                                        columnNumber: 37\n                                                                                                    }, this),\n                                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_10__.TooltipContent, {\n                                                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                                                            children: \"Available in Pro and Clipper plans\"\n                                                                                                        }, void 0, false, {\n                                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                                            lineNumber: 284,\n                                                                                                            columnNumber: 39\n                                                                                                        }, this)\n                                                                                                    }, void 0, false, {\n                                                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                                        lineNumber: 283,\n                                                                                                        columnNumber: 37\n                                                                                                    }, this)\n                                                                                                ]\n                                                                                            }, void 0, true, {\n                                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                                lineNumber: 277,\n                                                                                                columnNumber: 35\n                                                                                            }, this)\n                                                                                        }, void 0, false, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                            lineNumber: 276,\n                                                                                            columnNumber: 33\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                    lineNumber: 264,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                            lineNumber: 260,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 259,\n                                                                        columnNumber: 25\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center justify-between\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                                                                    htmlFor: \"smart-crop\",\n                                                                                    className: \"cursor-pointer text-luxury-green\",\n                                                                                    children: \"Smart Crop\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                    lineNumber: 295,\n                                                                                    columnNumber: 29\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_switch__WEBPACK_IMPORTED_MODULE_4__.Switch, {\n                                                                                    id: \"smart-crop\",\n                                                                                    checked: smartCrop,\n                                                                                    onCheckedChange: setSmartCrop,\n                                                                                    className: \"data-[state=checked]:bg-luxury-green\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                                    lineNumber: 298,\n                                                                                    columnNumber: 29\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                            lineNumber: 294,\n                                                                            columnNumber: 27\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 293,\n                                                                        columnNumber: 25\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 258,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 134,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 132,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                type: \"submit\",\n                                                className: \"w-full h-12 rounded-xl bg-gradient-to-r from-luxury-green to-luxury-green-dark hover:opacity-90 transition-all shadow-lg hover:shadow-xl\",\n                                                children: \"Snip It\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 310,\n                                                columnNumber: 19\n                                            }, this),\n                                            user?.plan !== \"clipper\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-xs text-gray-500 mt-2 text-center\",\n                                                children: [\n                                                    clipsRemaining,\n                                                    \" clips remaining \",\n                                                    user?.plan === \"free\" ? \"today\" : \"this month\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 317,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 97,\n                                        columnNumber: 17\n                                    }, this),\n                                    step === \"processing\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                                        className: \"w-full py-10 text-center\",\n                                        initial: {\n                                            opacity: 0,\n                                            scale: 0.95\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            scale: 1\n                                        },\n                                        transition: {\n                                            duration: 0.3\n                                        },\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col items-center justify-center gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative w-16 h-16\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-0 rounded-full border-4 border-luxury-green/20\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 333,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute inset-0 rounded-full border-4 border-luxury-green border-t-transparent animate-spin\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 334,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 332,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xl font-medium text-luxury-green\",\n                                                    children: \"Processing your video\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 336,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-500\",\n                                                    children: \"We're cropping and adding subtitles...\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 337,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 331,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 325,\n                                        columnNumber: 17\n                                    }, this),\n                                    step === \"result\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_12__.motion.div, {\n                                        className: \"w-full\",\n                                        initial: {\n                                            opacity: 0,\n                                            scale: 0.95\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            scale: 1\n                                        },\n                                        transition: {\n                                            duration: 0.3\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-between mb-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"text-xl font-medium text-luxury-green\",\n                                                        children: \"Your clip is ready!\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 350,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"ghost\",\n                                                        size: \"sm\",\n                                                        onClick: resetForm,\n                                                        className: \"text-luxury-green hover:text-luxury-green-dark hover:bg-luxury-green/10\",\n                                                        children: \"Create another\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 351,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 349,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"aspect-[9/16] bg-gradient-to-br from-luxury-gold/10 to-luxury-green/10 rounded-xl mb-6 mx-auto max-w-[280px] flex items-center justify-center border border-luxury-gold/20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center p-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-16 h-16 bg-luxury-green/20 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_Clock_Copy_Download_Info_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                className: \"h-8 w-8 text-luxury-green\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 364,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 363,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-500\",\n                                                            children: \"Video preview\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 366,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 362,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 361,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col sm:flex-row gap-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        className: \"flex-1 h-12 rounded-xl bg-gradient-to-r from-luxury-green to-luxury-green-dark hover:opacity-90 transition-all shadow-lg hover:shadow-xl\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_Clock_Copy_Download_Info_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                className: \"mr-2 h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 372,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"Download MP4\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 371,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: \"outline\",\n                                                        className: \"flex-1 h-12 rounded-xl border-2 border-luxury-green text-luxury-green hover:bg-luxury-green/10\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronDown_Clock_Copy_Download_Info_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                className: \"mr-2 h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 379,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            \"Copy for Shorts / Reels\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 375,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 370,\n                                                columnNumber: 19\n                                            }, this),\n                                            user?.plan !== \"clipper\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-4 p-3 bg-luxury-green/10 rounded-xl flex items-center justify-between border border-luxury-gold/20\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-luxury-green\",\n                                                        children: \"Save this clip to your storage\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 386,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_10__.TooltipProvider, {\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_10__.Tooltip, {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_10__.TooltipTrigger, {\n                                                                    asChild: true,\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                        size: \"sm\",\n                                                                        variant: \"ghost\",\n                                                                        className: \"text-luxury-green hover:text-luxury-green-dark hover:bg-luxury-green/20\",\n                                                                        disabled: true,\n                                                                        children: \"Save to Storage\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 390,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 389,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_10__.TooltipContent, {\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        children: \"Sign up for Clipper to save clips\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                        lineNumber: 400,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                    lineNumber: 399,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 388,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 387,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 385,\n                                                columnNumber: 21\n                                            }, this),\n                                            user?.plan === \"clipper\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-4 p-3 bg-luxury-green/10 rounded-xl flex items-center justify-between border border-luxury-gold/20\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-luxury-green\",\n                                                        children: \"Save this clip to your storage\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 409,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        size: \"sm\",\n                                                        variant: \"ghost\",\n                                                        className: \"text-luxury-green hover:text-luxury-green-dark hover:bg-luxury-green/20\",\n                                                        children: \"Save to Storage\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                        lineNumber: 410,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 408,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                        lineNumber: 343,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 95,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 93,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full lg:w-1/3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.Card, {\n                                className: \"bg-white rounded-2xl shadow-sm overflow-hidden mb-6 border border-luxury-gold/10\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.CardContent, {\n                                    className: \"p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-medium mb-4 text-luxury-green\",\n                                            children: \"Recent Activity\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 428,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border-b border-gray-100 pb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium text-luxury-green\",\n                                                            children: \"summer_vlog_edit.mp4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 431,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: \"30s clip • Created 2 hours ago\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 432,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 430,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border-b border-gray-100 pb-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium text-luxury-green\",\n                                                            children: \"product_review_final.mp4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 435,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: \"15s clip • Created yesterday\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 436,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 434,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"font-medium text-luxury-green\",\n                                                            children: \"beach_travel_highlights.mp4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 439,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: \"60s clip • Created 3 days ago\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 440,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 438,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 429,\n                                            columnNumber: 15\n                                        }, this),\n                                        user?.plan === \"clipper\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-4 pt-4 border-t border-gray-100\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"ghost\",\n                                                className: \"w-full text-luxury-green hover:text-luxury-green-dark hover:bg-luxury-green/10\",\n                                                children: \"View All Clips\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 445,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 444,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-4 pt-4 border-t border-gray-100\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_10__.TooltipProvider, {\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_10__.Tooltip, {\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_10__.TooltipTrigger, {\n                                                            asChild: true,\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                variant: \"ghost\",\n                                                                className: \"w-full text-gray-400 hover:text-gray-500 hover:bg-gray-100\",\n                                                                disabled: true,\n                                                                children: \"View All Clips\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 457,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 456,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_10__.TooltipContent, {\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                children: \"Available in Clipper plan\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 466,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 465,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 455,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                lineNumber: 454,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 453,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 427,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 426,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.Card, {\n                                className: \"bg-white rounded-2xl shadow-sm overflow-hidden border border-luxury-gold/10\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_9__.CardContent, {\n                                    className: \"p-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-medium mb-4 text-luxury-green\",\n                                            children: \"Quick Tips\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 477,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-3 text-sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-start gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-5 h-5 rounded-full bg-luxury-green/20 flex items-center justify-center shrink-0 mt-0.5\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-luxury-green font-medium\",\n                                                                children: \"1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 481,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 480,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Use videos with clear audio for better subtitle accuracy\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 483,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 479,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-start gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-5 h-5 rounded-full bg-luxury-green/20 flex items-center justify-center shrink-0 mt-0.5\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-luxury-green font-medium\",\n                                                                children: \"2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 487,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 486,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Smart Crop works best with videos that have clear subjects\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 489,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 485,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    className: \"flex items-start gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-5 h-5 rounded-full bg-luxury-green/20 flex items-center justify-center shrink-0 mt-0.5\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-xs text-luxury-green font-medium\",\n                                                                children: \"3\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                                lineNumber: 493,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 492,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Gold subtitles often get higher engagement than white\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                            lineNumber: 495,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                                    lineNumber: 491,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 478,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            variant: \"link\",\n                                            className: \"mt-2 text-luxury-green hover:text-luxury-green-dark p-0 h-auto\",\n                                            children: \"View all tips\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                            lineNumber: 498,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                    lineNumber: 476,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                                lineNumber: 475,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                        lineNumber: 425,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n                lineNumber: 92,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\page.tsx\",\n        lineNumber: 75,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/dashboard/page.tsx\n");

/***/ }),

/***/ "(ssr)/./components/dashboard/dashboard-header.tsx":
/*!***************************************************!*\
  !*** ./components/dashboard/dashboard-header.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DashboardHeader: () => (/* binding */ DashboardHeader)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Crown_Download_HelpCircle_LogOut_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Crown,Download,HelpCircle,LogOut,Settings,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/download.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Crown_Download_HelpCircle_LogOut_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Crown,Download,HelpCircle,LogOut,Settings,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Crown_Download_HelpCircle_LogOut_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Crown,Download,HelpCircle,LogOut,Settings,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle-help.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Crown_Download_HelpCircle_LogOut_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Crown,Download,HelpCircle,LogOut,Settings,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Crown_Download_HelpCircle_LogOut_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Crown,Download,HelpCircle,LogOut,Settings,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Crown_Download_HelpCircle_LogOut_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Crown,Download,HelpCircle,LogOut,Settings,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Crown_Download_HelpCircle_LogOut_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Crown,Download,HelpCircle,LogOut,Settings,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_Bell_CreditCard_Crown_Download_HelpCircle_LogOut_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bell,CreditCard,Crown,Download,HelpCircle,LogOut,Settings,User!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(ssr)/./components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _lib_store__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/store */ \"(ssr)/./lib/store.ts\");\n/* harmony import */ var _notifications_panel__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./notifications-panel */ \"(ssr)/./components/dashboard/notifications-panel.tsx\");\n/* __next_internal_client_entry_do_not_use__ DashboardHeader auto */ \n\n\n\n\n\n\n\nfunction DashboardHeader() {\n    const [notificationsOpen, setNotificationsOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [unreadCount, setUnreadCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(3);\n    const notificationsPanelRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const bellButtonRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const { user, logout } = (0,_lib_store__WEBPACK_IMPORTED_MODULE_5__.useUserStore)();\n    // Sample notifications\n    const [notifications, setNotifications] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([\n        {\n            id: \"1\",\n            title: \"Clip Processing Complete\",\n            message: \"Your clip 'summer_vlog_edit.mp4' is ready to download.\",\n            time: \"2 minutes ago\",\n            read: false,\n            type: \"clip\",\n            action: {\n                label: \"Download\",\n                icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Crown_Download_HelpCircle_LogOut_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                    className: \"h-4 w-4 mr-1\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\dashboard-header.tsx\",\n                    lineNumber: 45,\n                    columnNumber: 15\n                }, this),\n                onClick: ()=>{\n                    console.log(\"Download clip\");\n                }\n            }\n        },\n        {\n            id: \"2\",\n            title: \"Welcome to ClipSnip!\",\n            message: \"Thanks for joining. Start creating your first viral clip today.\",\n            time: \"1 hour ago\",\n            read: false,\n            type: \"system\"\n        },\n        {\n            id: \"3\",\n            title: \"New Feature Available\",\n            message: \"Try our new Smart Crop feature for better video framing.\",\n            time: \"Yesterday\",\n            read: false,\n            type: \"update\"\n        },\n        {\n            id: \"4\",\n            title: \"Subscription Renewed\",\n            message: \"Your Pro subscription has been successfully renewed.\",\n            time: \"3 days ago\",\n            read: true,\n            type: \"system\"\n        }\n    ]);\n    // Close notifications panel when clicking outside\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleClickOutside = (event)=>{\n            if (notificationsPanelRef.current && !notificationsPanelRef.current.contains(event.target) && bellButtonRef.current && !bellButtonRef.current.contains(event.target)) {\n                setNotificationsOpen(false);\n            }\n        };\n        document.addEventListener(\"mousedown\", handleClickOutside);\n        return ()=>{\n            document.removeEventListener(\"mousedown\", handleClickOutside);\n        };\n    }, []);\n    // Update unread count when notifications change\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const unreadNotifications = notifications.filter((notification)=>!notification.read);\n        setUnreadCount(unreadNotifications.length);\n    }, [\n        notifications\n    ]);\n    const handleMarkAllAsRead = ()=>{\n        setNotifications(notifications.map((notification)=>({\n                ...notification,\n                read: true\n            })));\n    };\n    const handleDismissNotification = (id)=>{\n        setNotifications(notifications.filter((notification)=>notification.id !== id));\n    };\n    const getPlanBadge = ()=>{\n        switch(user?.plan){\n            case \"clipper\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-xs bg-gradient-to-r from-luxury-gold to-luxury-gold-dark text-luxury-green px-3 py-1 rounded-full font-bold flex items-center gap-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Crown_Download_HelpCircle_LogOut_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            className: \"h-3 w-3\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\dashboard-header.tsx\",\n                            lineNumber: 120,\n                            columnNumber: 13\n                        }, this),\n                        \"Clipper\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\dashboard-header.tsx\",\n                    lineNumber: 119,\n                    columnNumber: 11\n                }, this);\n            case \"pro\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-xs bg-luxury-green/20 text-luxury-green px-3 py-1 rounded-full font-medium\",\n                    children: \"Pro\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\dashboard-header.tsx\",\n                    lineNumber: 126,\n                    columnNumber: 11\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-xs bg-gray-100 text-gray-600 px-3 py-1 rounded-full font-medium\",\n                    children: \"Free\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\dashboard-header.tsx\",\n                    lineNumber: 129,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n        className: \"h-16 border-b border-luxury-gold/20 px-6 flex items-center justify-between bg-gradient-to-r from-luxury-green via-luxury-green-light to-luxury-green text-white shadow-lg\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-8 w-8 rounded-full bg-luxury-gold/20 flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Crown_Download_HelpCircle_LogOut_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                            className: \"h-4 w-4 text-luxury-gold\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\dashboard-header.tsx\",\n                            lineNumber: 137,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\dashboard-header.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"font-bold text-lg\",\n                                children: \"Dashboard\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\dashboard-header.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-luxury-gold\",\n                                children: \"Create with precision\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\dashboard-header.tsx\",\n                                lineNumber: 141,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\dashboard-header.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\dashboard-header.tsx\",\n                lineNumber: 135,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center gap-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        variant: \"ghost\",\n                        size: \"icon\",\n                        className: \"rounded-full h-10 w-10 hover:bg-luxury-gold/20 text-luxury-gold hover:text-luxury-gold transition-all duration-200\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Crown_Download_HelpCircle_LogOut_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                            className: \"h-5 w-5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\dashboard-header.tsx\",\n                            lineNumber: 151,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\dashboard-header.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                ref: bellButtonRef,\n                                variant: \"ghost\",\n                                size: \"icon\",\n                                className: \"rounded-full h-10 w-10 hover:bg-luxury-gold/20 text-luxury-gold hover:text-luxury-gold transition-all duration-200\",\n                                onClick: ()=>setNotificationsOpen(!notificationsOpen),\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Crown_Download_HelpCircle_LogOut_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\dashboard-header.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 13\n                                    }, this),\n                                    unreadCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"absolute -top-1 -right-1 h-5 w-5 bg-luxury-gold text-luxury-green rounded-full text-xs flex items-center justify-center font-bold shadow-lg\",\n                                        children: unreadCount\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\dashboard-header.tsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\dashboard-header.tsx\",\n                                lineNumber: 155,\n                                columnNumber: 11\n                            }, this),\n                            notificationsOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                ref: notificationsPanelRef,\n                                className: \"absolute right-0 mt-2 z-50\",\n                                style: {\n                                    width: \"380px\",\n                                    maxWidth: \"calc(100vw - 40px)\"\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_notifications_panel__WEBPACK_IMPORTED_MODULE_6__.NotificationsPanel, {\n                                    notifications: notifications,\n                                    onMarkAllAsRead: handleMarkAllAsRead,\n                                    onDismiss: handleDismissNotification,\n                                    onClose: ()=>setNotificationsOpen(false)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\dashboard-header.tsx\",\n                                    lineNumber: 176,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\dashboard-header.tsx\",\n                                lineNumber: 171,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\dashboard-header.tsx\",\n                        lineNumber: 154,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenu, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuTrigger, {\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"icon\",\n                                    className: \"rounded-full h-10 w-10 bg-luxury-gold/20 hover:bg-luxury-gold/30 text-luxury-gold hover:text-luxury-gold transition-all duration-200 border border-luxury-gold/30\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Crown_Download_HelpCircle_LogOut_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"h-5 w-5\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\dashboard-header.tsx\",\n                                        lineNumber: 193,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\dashboard-header.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\dashboard-header.tsx\",\n                                lineNumber: 187,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuContent, {\n                                align: \"end\",\n                                className: \"w-64 rounded-xl border-luxury-gold/20 bg-white shadow-2xl\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"px-4 py-3 border-b border-luxury-gold/20 bg-gradient-to-r from-luxury-green/5 to-luxury-gold/5\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"font-bold text-luxury-green\",\n                                                children: user?.name || \"Guest User\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\dashboard-header.tsx\",\n                                                lineNumber: 198,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600 truncate\",\n                                                children: user?.email || \"<EMAIL>\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\dashboard-header.tsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-2\",\n                                                children: getPlanBadge()\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\dashboard-header.tsx\",\n                                                lineNumber: 200,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\dashboard-header.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: \"/dashboard/plans\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuItem, {\n                                            className: \"cursor-pointer hover:bg-luxury-green/5 text-luxury-green\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Crown_Download_HelpCircle_LogOut_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\dashboard-header.tsx\",\n                                                    lineNumber: 204,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Subscription\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\dashboard-header.tsx\",\n                                                    lineNumber: 205,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\dashboard-header.tsx\",\n                                            lineNumber: 203,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\dashboard-header.tsx\",\n                                        lineNumber: 202,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                        href: \"/dashboard/settings\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuItem, {\n                                            className: \"cursor-pointer hover:bg-luxury-green/5 text-luxury-green\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Crown_Download_HelpCircle_LogOut_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                    className: \"mr-2 h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\dashboard-header.tsx\",\n                                                    lineNumber: 210,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    children: \"Settings\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\dashboard-header.tsx\",\n                                                    lineNumber: 211,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\dashboard-header.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\dashboard-header.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_4__.DropdownMenuItem, {\n                                        className: \"cursor-pointer text-red-600 hover:text-red-700 hover:bg-red-50\",\n                                        onClick: logout,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bell_CreditCard_Crown_Download_HelpCircle_LogOut_Settings_User_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"mr-2 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\dashboard-header.tsx\",\n                                                lineNumber: 218,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Logout\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\dashboard-header.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\dashboard-header.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\dashboard-header.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\dashboard-header.tsx\",\n                        lineNumber: 186,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\dashboard-header.tsx\",\n                lineNumber: 145,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\dashboard-header.tsx\",\n        lineNumber: 134,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/dashboard/dashboard-header.tsx\n");

/***/ }),

/***/ "(ssr)/./components/dashboard/dashboard-nav.tsx":
/*!************************************************!*\
  !*** ./components/dashboard/dashboard-nav.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DashboardNav: () => (/* binding */ DashboardNav)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_CreditCard_Crown_FileQuestion_HardDrive_Home_LogOut_Scissors_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,CreditCard,Crown,FileQuestion,HardDrive,Home,LogOut,Scissors,Settings!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_CreditCard_Crown_FileQuestion_HardDrive_Home_LogOut_Scissors_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,CreditCard,Crown,FileQuestion,HardDrive,Home,LogOut,Scissors,Settings!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/hard-drive.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_CreditCard_Crown_FileQuestion_HardDrive_Home_LogOut_Scissors_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,CreditCard,Crown,FileQuestion,HardDrive,Home,LogOut,Scissors,Settings!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-question.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_CreditCard_Crown_FileQuestion_HardDrive_Home_LogOut_Scissors_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,CreditCard,Crown,FileQuestion,HardDrive,Home,LogOut,Scissors,Settings!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_CreditCard_Crown_FileQuestion_HardDrive_Home_LogOut_Scissors_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,CreditCard,Crown,FileQuestion,HardDrive,Home,LogOut,Scissors,Settings!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/credit-card.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_CreditCard_Crown_FileQuestion_HardDrive_Home_LogOut_Scissors_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,CreditCard,Crown,FileQuestion,HardDrive,Home,LogOut,Scissors,Settings!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/scissors.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_CreditCard_Crown_FileQuestion_HardDrive_Home_LogOut_Scissors_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,CreditCard,Crown,FileQuestion,HardDrive,Home,LogOut,Scissors,Settings!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_CreditCard_Crown_FileQuestion_HardDrive_Home_LogOut_Scissors_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,CreditCard,Crown,FileQuestion,HardDrive,Home,LogOut,Scissors,Settings!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_CreditCard_Crown_FileQuestion_HardDrive_Home_LogOut_Scissors_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,CreditCard,Crown,FileQuestion,HardDrive,Home,LogOut,Scissors,Settings!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_ChevronLeft_ChevronRight_CreditCard_Crown_FileQuestion_HardDrive_Home_LogOut_Scissors_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ChevronLeft,ChevronRight,CreditCard,Crown,FileQuestion,HardDrive,Home,LogOut,Scissors,Settings!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* harmony import */ var _lib_store__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/store */ \"(ssr)/./lib/store.ts\");\n/* harmony import */ var _components_ui_tooltip__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/tooltip */ \"(ssr)/./components/ui/tooltip.tsx\");\n/* __next_internal_client_entry_do_not_use__ DashboardNav auto */ \n\n\n\n\n\n\n\n\nfunction DashboardNav() {\n    const pathname = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.usePathname)();\n    const [collapsed, setCollapsed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { user } = (0,_lib_store__WEBPACK_IMPORTED_MODULE_6__.useUserStore)();\n    const navItems = [\n        {\n            name: \"Dashboard\",\n            href: \"/dashboard\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_CreditCard_Crown_FileQuestion_HardDrive_Home_LogOut_Scissors_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\dashboard-nav.tsx\",\n                lineNumber: 32,\n                columnNumber: 13\n            }, this)\n        },\n        {\n            name: \"Storage\",\n            href: \"/dashboard/storage\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_CreditCard_Crown_FileQuestion_HardDrive_Home_LogOut_Scissors_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\dashboard-nav.tsx\",\n                lineNumber: 37,\n                columnNumber: 13\n            }, this),\n            premium: true,\n            premiumPlan: \"clipper\"\n        },\n        {\n            name: \"FAQ & Guides\",\n            href: \"/dashboard/faq\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_CreditCard_Crown_FileQuestion_HardDrive_Home_LogOut_Scissors_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\dashboard-nav.tsx\",\n                lineNumber: 44,\n                columnNumber: 13\n            }, this)\n        },\n        {\n            name: \"Settings\",\n            href: \"/dashboard/settings\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_CreditCard_Crown_FileQuestion_HardDrive_Home_LogOut_Scissors_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\dashboard-nav.tsx\",\n                lineNumber: 49,\n                columnNumber: 13\n            }, this)\n        },\n        {\n            name: \"Plans\",\n            href: \"/dashboard/plans\",\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_CreditCard_Crown_FileQuestion_HardDrive_Home_LogOut_Scissors_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                className: \"h-5 w-5\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\dashboard-nav.tsx\",\n                lineNumber: 54,\n                columnNumber: 13\n            }, this)\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"aside\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"bg-gradient-to-b from-luxury-green to-luxury-green-dark text-white flex flex-col transition-all duration-300 shadow-2xl border-r border-luxury-gold/20\", collapsed ? \"w-20\" : \"w-64\"),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"h-16 flex items-center px-4 border-b border-luxury-gold/20 justify-between bg-gradient-to-r from-luxury-green-dark/50 to-luxury-green/50\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        href: \"/dashboard\",\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_CreditCard_Crown_FileQuestion_HardDrive_Home_LogOut_Scissors_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"h-6 w-6 text-luxury-gold\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\dashboard-nav.tsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute -top-1 -right-1 w-3 h-3 bg-luxury-gold rounded-full flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_CreditCard_Crown_FileQuestion_HardDrive_Home_LogOut_Scissors_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-2 w-2 text-luxury-green\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\dashboard-nav.tsx\",\n                                            lineNumber: 71,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\dashboard-nav.tsx\",\n                                        lineNumber: 70,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\dashboard-nav.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 11\n                            }, this),\n                            !collapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"font-bold text-lg\",\n                                children: \"clipsnip\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\dashboard-nav.tsx\",\n                                lineNumber: 74,\n                                columnNumber: 26\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\dashboard-nav.tsx\",\n                        lineNumber: 67,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        variant: \"ghost\",\n                        size: \"icon\",\n                        className: \"rounded-full h-8 w-8 text-luxury-gold hover:bg-luxury-gold/20 hover:text-luxury-gold\",\n                        onClick: ()=>setCollapsed(!collapsed),\n                        children: collapsed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_CreditCard_Crown_FileQuestion_HardDrive_Home_LogOut_Scissors_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                            size: 16\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\dashboard-nav.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 24\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_CreditCard_Crown_FileQuestion_HardDrive_Home_LogOut_Scissors_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                            size: 16\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\dashboard-nav.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 53\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\dashboard-nav.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\dashboard-nav.tsx\",\n                lineNumber: 66,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"flex-1 p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                    className: \"space-y-2\",\n                    children: navItems.map((item)=>{\n                        if (item.premium && (!user || user.plan !== item.premiumPlan)) {\n                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_7__.TooltipProvider, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_7__.Tooltip, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_7__.TooltipTrigger, {\n                                                asChild: true,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"flex items-center gap-3 px-3 py-3 rounded-xl text-white/50 cursor-not-allowed transition-all duration-200\", collapsed ? \"justify-center\" : \"\"),\n                                                    children: [\n                                                        item.icon,\n                                                        !collapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center justify-between flex-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: item.name\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\dashboard-nav.tsx\",\n                                                                    lineNumber: 105,\n                                                                    columnNumber: 31\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"text-xs bg-luxury-gold/20 text-luxury-gold px-2 py-1 rounded-full\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_CreditCard_Crown_FileQuestion_HardDrive_Home_LogOut_Scissors_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                        className: \"h-3 w-3\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\dashboard-nav.tsx\",\n                                                                        lineNumber: 107,\n                                                                        columnNumber: 33\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\dashboard-nav.tsx\",\n                                                                    lineNumber: 106,\n                                                                    columnNumber: 31\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\dashboard-nav.tsx\",\n                                                            lineNumber: 104,\n                                                            columnNumber: 29\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\dashboard-nav.tsx\",\n                                                    lineNumber: 96,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\dashboard-nav.tsx\",\n                                                lineNumber: 95,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tooltip__WEBPACK_IMPORTED_MODULE_7__.TooltipContent, {\n                                                side: \"right\",\n                                                className: \"bg-luxury-green border-luxury-gold/20\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    children: \"Available in Clipper plan\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\dashboard-nav.tsx\",\n                                                    lineNumber: 114,\n                                                    columnNumber: 25\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\dashboard-nav.tsx\",\n                                                lineNumber: 113,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\dashboard-nav.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 21\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\dashboard-nav.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 19\n                                }, this)\n                            }, item.href, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\dashboard-nav.tsx\",\n                                lineNumber: 92,\n                                columnNumber: 17\n                            }, this);\n                        }\n                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: item.href,\n                                className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"flex items-center gap-3 px-3 py-3 rounded-xl transition-all duration-200 group\", pathname === item.href ? \"bg-luxury-green-light/30 text-luxury-gold border border-luxury-gold/30\" : \"text-white/80 hover:bg-luxury-green-light/20 hover:text-luxury-gold\", collapsed ? \"justify-center\" : \"\"),\n                                title: collapsed ? item.name : undefined,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"transition-transform duration-200 group-hover:scale-105\",\n                                        children: item.icon\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\dashboard-nav.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 19\n                                    }, this),\n                                    !collapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: item.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\dashboard-nav.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 34\n                                    }, this),\n                                    !collapsed && item.premium && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"ml-auto text-xs bg-luxury-gold/20 text-luxury-gold px-2 py-1 rounded-full flex items-center gap-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_CreditCard_Crown_FileQuestion_HardDrive_Home_LogOut_Scissors_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"h-3 w-3\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\dashboard-nav.tsx\",\n                                                lineNumber: 139,\n                                                columnNumber: 23\n                                            }, this),\n                                            \"Premium\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\dashboard-nav.tsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\dashboard-nav.tsx\",\n                                lineNumber: 124,\n                                columnNumber: 17\n                            }, this)\n                        }, item.href, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\dashboard-nav.tsx\",\n                            lineNumber: 123,\n                            columnNumber: 15\n                        }, this);\n                    })\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\dashboard-nav.tsx\",\n                    lineNumber: 88,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\dashboard-nav.tsx\",\n                lineNumber: 87,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 border-t border-luxury-gold/20 bg-gradient-to-r from-luxury-green-dark/50 to-luxury-green/50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                    variant: \"ghost\",\n                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"w-full justify-start text-white/80 hover:bg-luxury-gold/10 hover:text-luxury-gold transition-all duration-200\", collapsed ? \"justify-center\" : \"\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChevronLeft_ChevronRight_CreditCard_Crown_FileQuestion_HardDrive_Home_LogOut_Scissors_Settings_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                            className: \"h-5 w-5\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\dashboard-nav.tsx\",\n                            lineNumber: 159,\n                            columnNumber: 11\n                        }, this),\n                        !collapsed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"ml-2 font-medium\",\n                            children: \"Logout\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\dashboard-nav.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 26\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\dashboard-nav.tsx\",\n                    lineNumber: 152,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\dashboard-nav.tsx\",\n                lineNumber: 151,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\dashboard-nav.tsx\",\n        lineNumber: 59,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/dashboard/dashboard-nav.tsx\n");

/***/ }),

/***/ "(ssr)/./components/dashboard/notifications-panel.tsx":
/*!******************************************************!*\
  !*** ./components/dashboard/notifications-panel.tsx ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NotificationsPanel: () => (/* binding */ NotificationsPanel)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/card */ \"(ssr)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(ssr)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Clock_Crown_FileText_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Crown,FileText,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Crown_FileText_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Crown,FileText,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Crown_FileText_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Crown,FileText,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_Clock_Crown_FileText_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Clock,Crown,FileText,X!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ NotificationsPanel auto */ \n\n\n\n\nfunction NotificationsPanel({ notifications, onMarkAllAsRead, onDismiss, onClose }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.Card, {\n        className: \"w-[380px] rounded-xl shadow-2xl overflow-hidden border border-luxury-gold/20\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-gradient-to-r from-luxury-green to-luxury-green-light px-4 py-3 text-white flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Crown_FileText_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                className: \"h-4 w-4 text-luxury-gold\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\notifications-panel.tsx\",\n                                lineNumber: 35,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"font-bold\",\n                                children: \"Notifications\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\notifications-panel.tsx\",\n                                lineNumber: 36,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\notifications-panel.tsx\",\n                        lineNumber: 34,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"ghost\",\n                                size: \"sm\",\n                                className: \"h-8 text-white hover:text-luxury-gold hover:bg-luxury-gold/20 transition-all duration-200\",\n                                onClick: onMarkAllAsRead,\n                                children: \"Mark all as read\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\notifications-panel.tsx\",\n                                lineNumber: 39,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"ghost\",\n                                size: \"icon\",\n                                className: \"h-8 w-8 text-white hover:text-luxury-gold hover:bg-luxury-gold/20 transition-all duration-200\",\n                                onClick: onClose,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Crown_FileText_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\notifications-panel.tsx\",\n                                    lineNumber: 53,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\notifications-panel.tsx\",\n                                lineNumber: 47,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\notifications-panel.tsx\",\n                        lineNumber: 38,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\notifications-panel.tsx\",\n                lineNumber: 33,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_1__.CardContent, {\n                className: \"p-0 max-h-[400px] overflow-y-auto\",\n                children: notifications.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"py-8 text-center text-gray-500\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Crown_FileText_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                            className: \"h-8 w-8 mx-auto mb-2 text-luxury-gold/50\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\notifications-panel.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: \"No notifications\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\notifications-panel.tsx\",\n                            lineNumber: 62,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\notifications-panel.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"divide-y divide-luxury-gold/10\",\n                    children: notifications.map((notification)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"p-4 hover:bg-gradient-to-r hover:from-luxury-gold/5 hover:to-luxury-green/5 transition-all duration-200 relative\", !notification.read && \"bg-gradient-to-r from-luxury-gold/10 to-luxury-green/10 border-l-4 border-luxury-gold\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-start gap-3\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(\"w-10 h-10 rounded-full flex items-center justify-center shrink-0 shadow-md\", notification.type === \"clip\" && \"bg-gradient-to-br from-luxury-green to-luxury-green-light text-white\", notification.type === \"system\" && \"bg-gradient-to-br from-blue-500 to-blue-600 text-white\", notification.type === \"update\" && \"bg-gradient-to-br from-luxury-gold to-luxury-gold-dark text-luxury-green\"),\n                                            children: [\n                                                notification.type === \"clip\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Crown_FileText_X_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\notifications-panel.tsx\",\n                                                    lineNumber: 86,\n                                                    columnNumber: 54\n                                                }, this),\n                                                notification.type === \"system\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Crown_FileText_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\notifications-panel.tsx\",\n                                                    lineNumber: 87,\n                                                    columnNumber: 56\n                                                }, this),\n                                                notification.type === \"update\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Crown_FileText_X_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    className: \"h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\notifications-panel.tsx\",\n                                                    lineNumber: 88,\n                                                    columnNumber: 56\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\notifications-panel.tsx\",\n                                            lineNumber: 76,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-bold text-luxury-green\",\n                                                    children: notification.title\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\notifications-panel.tsx\",\n                                                    lineNumber: 91,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-600 mb-2 leading-relaxed\",\n                                                    children: notification.message\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\notifications-panel.tsx\",\n                                                    lineNumber: 92,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-xs text-gray-500 font-medium\",\n                                                            children: notification.time\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\notifications-panel.tsx\",\n                                                            lineNumber: 94,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        notification.action && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            variant: \"ghost\",\n                                                            size: \"sm\",\n                                                            className: \"h-7 text-luxury-green hover:text-luxury-green-dark hover:bg-luxury-green/10 transition-all duration-200\",\n                                                            onClick: notification.action.onClick,\n                                                            children: [\n                                                                notification.action.icon,\n                                                                notification.action.label\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\notifications-panel.tsx\",\n                                                            lineNumber: 96,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\notifications-panel.tsx\",\n                                                    lineNumber: 93,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\notifications-panel.tsx\",\n                                            lineNumber: 90,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\notifications-panel.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                    variant: \"ghost\",\n                                    size: \"icon\",\n                                    className: \"h-6 w-6 absolute top-2 right-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-full transition-all duration-200\",\n                                    onClick: ()=>onDismiss(notification.id),\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Clock_Crown_FileText_X_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        className: \"h-3 w-3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\notifications-panel.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\notifications-panel.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, notification.id, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\notifications-panel.tsx\",\n                            lineNumber: 67,\n                            columnNumber: 15\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\notifications-panel.tsx\",\n                    lineNumber: 65,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\notifications-panel.tsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\dashboard\\\\notifications-panel.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/dashboard/notifications-panel.tsx\n");

/***/ }),

/***/ "(ssr)/./components/theme-provider.tsx":
/*!***************************************!*\
  !*** ./components/theme-provider.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ ThemeProvider auto */ \n\n\nfunction ThemeProvider({ children, ...props }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n        ...props,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\theme-provider.tsx\",\n        lineNumber: 10,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3RoZW1lLXByb3ZpZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBRThCO0FBSVY7QUFFYixTQUFTQyxjQUFjLEVBQUVFLFFBQVEsRUFBRSxHQUFHQyxPQUEyQjtJQUN0RSxxQkFBTyw4REFBQ0Ysc0RBQWtCQTtRQUFFLEdBQUdFLEtBQUs7a0JBQUdEOzs7Ozs7QUFDekMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9teS12MC1wcm9qZWN0Ly4vY29tcG9uZW50cy90aGVtZS1wcm92aWRlci50c3g/OTI4OSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCdcblxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSAncmVhY3QnXG5pbXBvcnQge1xuICBUaGVtZVByb3ZpZGVyIGFzIE5leHRUaGVtZXNQcm92aWRlcixcbiAgdHlwZSBUaGVtZVByb3ZpZGVyUHJvcHMsXG59IGZyb20gJ25leHQtdGhlbWVzJ1xuXG5leHBvcnQgZnVuY3Rpb24gVGhlbWVQcm92aWRlcih7IGNoaWxkcmVuLCAuLi5wcm9wcyB9OiBUaGVtZVByb3ZpZGVyUHJvcHMpIHtcbiAgcmV0dXJuIDxOZXh0VGhlbWVzUHJvdmlkZXIgey4uLnByb3BzfT57Y2hpbGRyZW59PC9OZXh0VGhlbWVzUHJvdmlkZXI+XG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJUaGVtZVByb3ZpZGVyIiwiTmV4dFRoZW1lc1Byb3ZpZGVyIiwiY2hpbGRyZW4iLCJwcm9wcyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./components/theme-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/button.tsx":
/*!**********************************!*\
  !*** ./components/ui/button.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(ssr)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"text-primary underline-offset-4 hover:underline\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-md px-3\",\n            lg: \"h-11 rounded-md px-8\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 46,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/button.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/card.tsx":
/*!********************************!*\
  !*** ./components/ui/card.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 24,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 36,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 51,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 63,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 71,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/card.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/dropdown-menu.tsx":
/*!*****************************************!*\
  !*** ./components/ui/dropdown-menu.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DropdownMenu: () => (/* binding */ DropdownMenu),\n/* harmony export */   DropdownMenuCheckboxItem: () => (/* binding */ DropdownMenuCheckboxItem),\n/* harmony export */   DropdownMenuContent: () => (/* binding */ DropdownMenuContent),\n/* harmony export */   DropdownMenuGroup: () => (/* binding */ DropdownMenuGroup),\n/* harmony export */   DropdownMenuItem: () => (/* binding */ DropdownMenuItem),\n/* harmony export */   DropdownMenuLabel: () => (/* binding */ DropdownMenuLabel),\n/* harmony export */   DropdownMenuPortal: () => (/* binding */ DropdownMenuPortal),\n/* harmony export */   DropdownMenuRadioGroup: () => (/* binding */ DropdownMenuRadioGroup),\n/* harmony export */   DropdownMenuRadioItem: () => (/* binding */ DropdownMenuRadioItem),\n/* harmony export */   DropdownMenuSeparator: () => (/* binding */ DropdownMenuSeparator),\n/* harmony export */   DropdownMenuShortcut: () => (/* binding */ DropdownMenuShortcut),\n/* harmony export */   DropdownMenuSub: () => (/* binding */ DropdownMenuSub),\n/* harmony export */   DropdownMenuSubContent: () => (/* binding */ DropdownMenuSubContent),\n/* harmony export */   DropdownMenuSubTrigger: () => (/* binding */ DropdownMenuSubTrigger),\n/* harmony export */   DropdownMenuTrigger: () => (/* binding */ DropdownMenuTrigger)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-dropdown-menu */ \"(ssr)/./node_modules/@radix-ui/react-dropdown-menu/dist/index.mjs\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronRight,Circle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronRight,Circle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Check,ChevronRight,Circle!=!lucide-react */ \"(ssr)/./node_modules/lucide-react/dist/esm/icons/circle.js\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ DropdownMenu,DropdownMenuTrigger,DropdownMenuContent,DropdownMenuItem,DropdownMenuCheckboxItem,DropdownMenuRadioItem,DropdownMenuLabel,DropdownMenuSeparator,DropdownMenuShortcut,DropdownMenuGroup,DropdownMenuPortal,DropdownMenuSub,DropdownMenuSubContent,DropdownMenuSubTrigger,DropdownMenuRadioGroup auto */ \n\n\n\n\nconst DropdownMenu = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Root;\nconst DropdownMenuTrigger = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Trigger;\nconst DropdownMenuGroup = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Group;\nconst DropdownMenuPortal = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Portal;\nconst DropdownMenuSub = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Sub;\nconst DropdownMenuRadioGroup = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.RadioGroup;\nconst DropdownMenuSubTrigger = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, inset, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.SubTrigger, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex cursor-default gap-2 select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\", inset && \"pl-8\", className),\n        ...props,\n        children: [\n            children,\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                className: \"ml-auto\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                lineNumber: 37,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 27,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuSubTrigger.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.SubTrigger.displayName;\nconst DropdownMenuSubContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.SubContent, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 47,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuSubContent.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.SubContent.displayName;\nconst DropdownMenuContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, sideOffset = 4, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Portal, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Content, {\n            ref: ref,\n            sideOffset: sideOffset,\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\", className),\n            ...props\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n            lineNumber: 64,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 63,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuContent.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Content.displayName;\nconst DropdownMenuItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, inset, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Item, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\", inset && \"pl-8\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 83,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuItem.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Item.displayName;\nconst DropdownMenuCheckboxItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, children, checked, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.CheckboxItem, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\", className),\n        checked: checked,\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.ItemIndicator, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                        className: \"h-4 w-4\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                        lineNumber: 110,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                    lineNumber: 109,\n                    columnNumber: 7\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                lineNumber: 108,\n                columnNumber: 5\n            }, undefined),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 99,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuCheckboxItem.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.CheckboxItem.displayName;\nconst DropdownMenuRadioItem = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, children, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.RadioItem, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\", className),\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                className: \"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.ItemIndicator, {\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Check_ChevronRight_Circle_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                        className: \"h-2 w-2 fill-current\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                        lineNumber: 133,\n                        columnNumber: 9\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                    lineNumber: 132,\n                    columnNumber: 7\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n                lineNumber: 131,\n                columnNumber: 5\n            }, undefined),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 123,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuRadioItem.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.RadioItem.displayName;\nconst DropdownMenuLabel = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, inset, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Label, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"px-2 py-1.5 text-sm font-semibold\", inset && \"pl-8\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 147,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuLabel.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Label.displayName;\nconst DropdownMenuSeparator = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Separator, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"-mx-1 my-1 h-px bg-muted\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 163,\n        columnNumber: 3\n    }, undefined));\nDropdownMenuSeparator.displayName = _radix_ui_react_dropdown_menu__WEBPACK_IMPORTED_MODULE_3__.Separator.displayName;\nconst DropdownMenuShortcut = ({ className, ...props })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"ml-auto text-xs tracking-widest opacity-60\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\ui\\\\dropdown-menu.tsx\",\n        lineNumber: 176,\n        columnNumber: 5\n    }, undefined);\n};\nDropdownMenuShortcut.displayName = \"DropdownMenuShortcut\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL2Ryb3Bkb3duLW1lbnUudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFOEI7QUFDd0M7QUFDWjtBQUUxQjtBQUVoQyxNQUFNTSxlQUFlTCwrREFBMEI7QUFFL0MsTUFBTU8sc0JBQXNCUCxrRUFBNkI7QUFFekQsTUFBTVMsb0JBQW9CVCxnRUFBMkI7QUFFckQsTUFBTVcscUJBQXFCWCxpRUFBNEI7QUFFdkQsTUFBTWEsa0JBQWtCYiw4REFBeUI7QUFFakQsTUFBTWUseUJBQXlCZixxRUFBZ0M7QUFFL0QsTUFBTWlCLHVDQUF5QmxCLDZDQUFnQixDQUs3QyxDQUFDLEVBQUVvQixTQUFTLEVBQUVDLEtBQUssRUFBRUMsUUFBUSxFQUFFLEdBQUdDLE9BQU8sRUFBRUMsb0JBQzNDLDhEQUFDdkIscUVBQWdDO1FBQy9CdUIsS0FBS0E7UUFDTEosV0FBV2YsOENBQUVBLENBQ1gsME1BQ0FnQixTQUFTLFFBQ1REO1FBRUQsR0FBR0csS0FBSzs7WUFFUkQ7MEJBQ0QsOERBQUNuQixxR0FBWUE7Z0JBQUNpQixXQUFVOzs7Ozs7Ozs7Ozs7QUFHNUJGLHVCQUF1QlEsV0FBVyxHQUNoQ3pCLHFFQUFnQyxDQUFDeUIsV0FBVztBQUU5QyxNQUFNQyx1Q0FBeUIzQiw2Q0FBZ0IsQ0FHN0MsQ0FBQyxFQUFFb0IsU0FBUyxFQUFFLEdBQUdHLE9BQU8sRUFBRUMsb0JBQzFCLDhEQUFDdkIscUVBQWdDO1FBQy9CdUIsS0FBS0E7UUFDTEosV0FBV2YsOENBQUVBLENBQ1gseWJBQ0FlO1FBRUQsR0FBR0csS0FBSzs7Ozs7O0FBR2JJLHVCQUF1QkQsV0FBVyxHQUNoQ3pCLHFFQUFnQyxDQUFDeUIsV0FBVztBQUU5QyxNQUFNRyxvQ0FBc0I3Qiw2Q0FBZ0IsQ0FHMUMsQ0FBQyxFQUFFb0IsU0FBUyxFQUFFVSxhQUFhLENBQUMsRUFBRSxHQUFHUCxPQUFPLEVBQUVDLG9CQUMxQyw4REFBQ3ZCLGlFQUE0QjtrQkFDM0IsNEVBQUNBLGtFQUE2QjtZQUM1QnVCLEtBQUtBO1lBQ0xNLFlBQVlBO1lBQ1pWLFdBQVdmLDhDQUFFQSxDQUNYLHliQUNBZTtZQUVELEdBQUdHLEtBQUs7Ozs7Ozs7Ozs7O0FBSWZNLG9CQUFvQkgsV0FBVyxHQUFHekIsa0VBQTZCLENBQUN5QixXQUFXO0FBRTNFLE1BQU1NLGlDQUFtQmhDLDZDQUFnQixDQUt2QyxDQUFDLEVBQUVvQixTQUFTLEVBQUVDLEtBQUssRUFBRSxHQUFHRSxPQUFPLEVBQUVDLG9CQUNqQyw4REFBQ3ZCLCtEQUEwQjtRQUN6QnVCLEtBQUtBO1FBQ0xKLFdBQVdmLDhDQUFFQSxDQUNYLHFTQUNBZ0IsU0FBUyxRQUNURDtRQUVELEdBQUdHLEtBQUs7Ozs7OztBQUdiUyxpQkFBaUJOLFdBQVcsR0FBR3pCLCtEQUEwQixDQUFDeUIsV0FBVztBQUVyRSxNQUFNUSx5Q0FBMkJsQyw2Q0FBZ0IsQ0FHL0MsQ0FBQyxFQUFFb0IsU0FBUyxFQUFFRSxRQUFRLEVBQUVhLE9BQU8sRUFBRSxHQUFHWixPQUFPLEVBQUVDLG9CQUM3Qyw4REFBQ3ZCLHVFQUFrQztRQUNqQ3VCLEtBQUtBO1FBQ0xKLFdBQVdmLDhDQUFFQSxDQUNYLHdPQUNBZTtRQUVGZSxTQUFTQTtRQUNSLEdBQUdaLEtBQUs7OzBCQUVULDhEQUFDYztnQkFBS2pCLFdBQVU7MEJBQ2QsNEVBQUNuQix3RUFBbUM7OEJBQ2xDLDRFQUFDQyxxR0FBS0E7d0JBQUNrQixXQUFVOzs7Ozs7Ozs7Ozs7Ozs7O1lBR3BCRTs7Ozs7OztBQUdMWSx5QkFBeUJSLFdBQVcsR0FDbEN6Qix1RUFBa0MsQ0FBQ3lCLFdBQVc7QUFFaEQsTUFBTWEsc0NBQXdCdkMsNkNBQWdCLENBRzVDLENBQUMsRUFBRW9CLFNBQVMsRUFBRUUsUUFBUSxFQUFFLEdBQUdDLE9BQU8sRUFBRUMsb0JBQ3BDLDhEQUFDdkIsb0VBQStCO1FBQzlCdUIsS0FBS0E7UUFDTEosV0FBV2YsOENBQUVBLENBQ1gsd09BQ0FlO1FBRUQsR0FBR0csS0FBSzs7MEJBRVQsOERBQUNjO2dCQUFLakIsV0FBVTswQkFDZCw0RUFBQ25CLHdFQUFtQzs4QkFDbEMsNEVBQUNHLHFHQUFNQTt3QkFBQ2dCLFdBQVU7Ozs7Ozs7Ozs7Ozs7Ozs7WUFHckJFOzs7Ozs7O0FBR0xpQixzQkFBc0JiLFdBQVcsR0FBR3pCLG9FQUErQixDQUFDeUIsV0FBVztBQUUvRSxNQUFNZSxrQ0FBb0J6Qyw2Q0FBZ0IsQ0FLeEMsQ0FBQyxFQUFFb0IsU0FBUyxFQUFFQyxLQUFLLEVBQUUsR0FBR0UsT0FBTyxFQUFFQyxvQkFDakMsOERBQUN2QixnRUFBMkI7UUFDMUJ1QixLQUFLQTtRQUNMSixXQUFXZiw4Q0FBRUEsQ0FDWCxxQ0FDQWdCLFNBQVMsUUFDVEQ7UUFFRCxHQUFHRyxLQUFLOzs7Ozs7QUFHYmtCLGtCQUFrQmYsV0FBVyxHQUFHekIsZ0VBQTJCLENBQUN5QixXQUFXO0FBRXZFLE1BQU1pQixzQ0FBd0IzQyw2Q0FBZ0IsQ0FHNUMsQ0FBQyxFQUFFb0IsU0FBUyxFQUFFLEdBQUdHLE9BQU8sRUFBRUMsb0JBQzFCLDhEQUFDdkIsb0VBQStCO1FBQzlCdUIsS0FBS0E7UUFDTEosV0FBV2YsOENBQUVBLENBQUMsNEJBQTRCZTtRQUN6QyxHQUFHRyxLQUFLOzs7Ozs7QUFHYm9CLHNCQUFzQmpCLFdBQVcsR0FBR3pCLG9FQUErQixDQUFDeUIsV0FBVztBQUUvRSxNQUFNbUIsdUJBQXVCLENBQUMsRUFDNUJ6QixTQUFTLEVBQ1QsR0FBR0csT0FDbUM7SUFDdEMscUJBQ0UsOERBQUNjO1FBQ0NqQixXQUFXZiw4Q0FBRUEsQ0FBQyw4Q0FBOENlO1FBQzNELEdBQUdHLEtBQUs7Ozs7OztBQUdmO0FBQ0FzQixxQkFBcUJuQixXQUFXLEdBQUc7QUFrQmxDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbXktdjAtcHJvamVjdC8uL2NvbXBvbmVudHMvdWkvZHJvcGRvd24tbWVudS50c3g/MDI0YSJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIlxuXG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIlxuaW1wb3J0ICogYXMgRHJvcGRvd25NZW51UHJpbWl0aXZlIGZyb20gXCJAcmFkaXgtdWkvcmVhY3QtZHJvcGRvd24tbWVudVwiXG5pbXBvcnQgeyBDaGVjaywgQ2hldnJvblJpZ2h0LCBDaXJjbGUgfSBmcm9tIFwibHVjaWRlLXJlYWN0XCJcblxuaW1wb3J0IHsgY24gfSBmcm9tIFwiQC9saWIvdXRpbHNcIlxuXG5jb25zdCBEcm9wZG93bk1lbnUgPSBEcm9wZG93bk1lbnVQcmltaXRpdmUuUm9vdFxuXG5jb25zdCBEcm9wZG93bk1lbnVUcmlnZ2VyID0gRHJvcGRvd25NZW51UHJpbWl0aXZlLlRyaWdnZXJcblxuY29uc3QgRHJvcGRvd25NZW51R3JvdXAgPSBEcm9wZG93bk1lbnVQcmltaXRpdmUuR3JvdXBcblxuY29uc3QgRHJvcGRvd25NZW51UG9ydGFsID0gRHJvcGRvd25NZW51UHJpbWl0aXZlLlBvcnRhbFxuXG5jb25zdCBEcm9wZG93bk1lbnVTdWIgPSBEcm9wZG93bk1lbnVQcmltaXRpdmUuU3ViXG5cbmNvbnN0IERyb3Bkb3duTWVudVJhZGlvR3JvdXAgPSBEcm9wZG93bk1lbnVQcmltaXRpdmUuUmFkaW9Hcm91cFxuXG5jb25zdCBEcm9wZG93bk1lbnVTdWJUcmlnZ2VyID0gUmVhY3QuZm9yd2FyZFJlZjxcbiAgUmVhY3QuRWxlbWVudFJlZjx0eXBlb2YgRHJvcGRvd25NZW51UHJpbWl0aXZlLlN1YlRyaWdnZXI+LFxuICBSZWFjdC5Db21wb25lbnRQcm9wc1dpdGhvdXRSZWY8dHlwZW9mIERyb3Bkb3duTWVudVByaW1pdGl2ZS5TdWJUcmlnZ2VyPiAmIHtcbiAgICBpbnNldD86IGJvb2xlYW5cbiAgfVxuPigoeyBjbGFzc05hbWUsIGluc2V0LCBjaGlsZHJlbiwgLi4ucHJvcHMgfSwgcmVmKSA9PiAoXG4gIDxEcm9wZG93bk1lbnVQcmltaXRpdmUuU3ViVHJpZ2dlclxuICAgIHJlZj17cmVmfVxuICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICBcImZsZXggY3Vyc29yLWRlZmF1bHQgZ2FwLTIgc2VsZWN0LW5vbmUgaXRlbXMtY2VudGVyIHJvdW5kZWQtc20gcHgtMiBweS0xLjUgdGV4dC1zbSBvdXRsaW5lLW5vbmUgZm9jdXM6YmctYWNjZW50IGRhdGEtW3N0YXRlPW9wZW5dOmJnLWFjY2VudCBbJl9zdmddOnBvaW50ZXItZXZlbnRzLW5vbmUgWyZfc3ZnXTpzaXplLTQgWyZfc3ZnXTpzaHJpbmstMFwiLFxuICAgICAgaW5zZXQgJiYgXCJwbC04XCIsXG4gICAgICBjbGFzc05hbWVcbiAgICApfVxuICAgIHsuLi5wcm9wc31cbiAgPlxuICAgIHtjaGlsZHJlbn1cbiAgICA8Q2hldnJvblJpZ2h0IGNsYXNzTmFtZT1cIm1sLWF1dG9cIiAvPlxuICA8L0Ryb3Bkb3duTWVudVByaW1pdGl2ZS5TdWJUcmlnZ2VyPlxuKSlcbkRyb3Bkb3duTWVudVN1YlRyaWdnZXIuZGlzcGxheU5hbWUgPVxuICBEcm9wZG93bk1lbnVQcmltaXRpdmUuU3ViVHJpZ2dlci5kaXNwbGF5TmFtZVxuXG5jb25zdCBEcm9wZG93bk1lbnVTdWJDb250ZW50ID0gUmVhY3QuZm9yd2FyZFJlZjxcbiAgUmVhY3QuRWxlbWVudFJlZjx0eXBlb2YgRHJvcGRvd25NZW51UHJpbWl0aXZlLlN1YkNvbnRlbnQ+LFxuICBSZWFjdC5Db21wb25lbnRQcm9wc1dpdGhvdXRSZWY8dHlwZW9mIERyb3Bkb3duTWVudVByaW1pdGl2ZS5TdWJDb250ZW50PlxuPigoeyBjbGFzc05hbWUsIC4uLnByb3BzIH0sIHJlZikgPT4gKFxuICA8RHJvcGRvd25NZW51UHJpbWl0aXZlLlN1YkNvbnRlbnRcbiAgICByZWY9e3JlZn1cbiAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgXCJ6LTUwIG1pbi13LVs4cmVtXSBvdmVyZmxvdy1oaWRkZW4gcm91bmRlZC1tZCBib3JkZXIgYmctcG9wb3ZlciBwLTEgdGV4dC1wb3BvdmVyLWZvcmVncm91bmQgc2hhZG93LWxnIGRhdGEtW3N0YXRlPW9wZW5dOmFuaW1hdGUtaW4gZGF0YS1bc3RhdGU9Y2xvc2VkXTphbmltYXRlLW91dCBkYXRhLVtzdGF0ZT1jbG9zZWRdOmZhZGUtb3V0LTAgZGF0YS1bc3RhdGU9b3Blbl06ZmFkZS1pbi0wIGRhdGEtW3N0YXRlPWNsb3NlZF06em9vbS1vdXQtOTUgZGF0YS1bc3RhdGU9b3Blbl06em9vbS1pbi05NSBkYXRhLVtzaWRlPWJvdHRvbV06c2xpZGUtaW4tZnJvbS10b3AtMiBkYXRhLVtzaWRlPWxlZnRdOnNsaWRlLWluLWZyb20tcmlnaHQtMiBkYXRhLVtzaWRlPXJpZ2h0XTpzbGlkZS1pbi1mcm9tLWxlZnQtMiBkYXRhLVtzaWRlPXRvcF06c2xpZGUtaW4tZnJvbS1ib3R0b20tMlwiLFxuICAgICAgY2xhc3NOYW1lXG4gICAgKX1cbiAgICB7Li4ucHJvcHN9XG4gIC8+XG4pKVxuRHJvcGRvd25NZW51U3ViQ29udGVudC5kaXNwbGF5TmFtZSA9XG4gIERyb3Bkb3duTWVudVByaW1pdGl2ZS5TdWJDb250ZW50LmRpc3BsYXlOYW1lXG5cbmNvbnN0IERyb3Bkb3duTWVudUNvbnRlbnQgPSBSZWFjdC5mb3J3YXJkUmVmPFxuICBSZWFjdC5FbGVtZW50UmVmPHR5cGVvZiBEcm9wZG93bk1lbnVQcmltaXRpdmUuQ29udGVudD4sXG4gIFJlYWN0LkNvbXBvbmVudFByb3BzV2l0aG91dFJlZjx0eXBlb2YgRHJvcGRvd25NZW51UHJpbWl0aXZlLkNvbnRlbnQ+XG4+KCh7IGNsYXNzTmFtZSwgc2lkZU9mZnNldCA9IDQsIC4uLnByb3BzIH0sIHJlZikgPT4gKFxuICA8RHJvcGRvd25NZW51UHJpbWl0aXZlLlBvcnRhbD5cbiAgICA8RHJvcGRvd25NZW51UHJpbWl0aXZlLkNvbnRlbnRcbiAgICAgIHJlZj17cmVmfVxuICAgICAgc2lkZU9mZnNldD17c2lkZU9mZnNldH1cbiAgICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICAgIFwiei01MCBtaW4tdy1bOHJlbV0gb3ZlcmZsb3ctaGlkZGVuIHJvdW5kZWQtbWQgYm9yZGVyIGJnLXBvcG92ZXIgcC0xIHRleHQtcG9wb3Zlci1mb3JlZ3JvdW5kIHNoYWRvdy1tZCBkYXRhLVtzdGF0ZT1vcGVuXTphbmltYXRlLWluIGRhdGEtW3N0YXRlPWNsb3NlZF06YW5pbWF0ZS1vdXQgZGF0YS1bc3RhdGU9Y2xvc2VkXTpmYWRlLW91dC0wIGRhdGEtW3N0YXRlPW9wZW5dOmZhZGUtaW4tMCBkYXRhLVtzdGF0ZT1jbG9zZWRdOnpvb20tb3V0LTk1IGRhdGEtW3N0YXRlPW9wZW5dOnpvb20taW4tOTUgZGF0YS1bc2lkZT1ib3R0b21dOnNsaWRlLWluLWZyb20tdG9wLTIgZGF0YS1bc2lkZT1sZWZ0XTpzbGlkZS1pbi1mcm9tLXJpZ2h0LTIgZGF0YS1bc2lkZT1yaWdodF06c2xpZGUtaW4tZnJvbS1sZWZ0LTIgZGF0YS1bc2lkZT10b3BdOnNsaWRlLWluLWZyb20tYm90dG9tLTJcIixcbiAgICAgICAgY2xhc3NOYW1lXG4gICAgICApfVxuICAgICAgey4uLnByb3BzfVxuICAgIC8+XG4gIDwvRHJvcGRvd25NZW51UHJpbWl0aXZlLlBvcnRhbD5cbikpXG5Ecm9wZG93bk1lbnVDb250ZW50LmRpc3BsYXlOYW1lID0gRHJvcGRvd25NZW51UHJpbWl0aXZlLkNvbnRlbnQuZGlzcGxheU5hbWVcblxuY29uc3QgRHJvcGRvd25NZW51SXRlbSA9IFJlYWN0LmZvcndhcmRSZWY8XG4gIFJlYWN0LkVsZW1lbnRSZWY8dHlwZW9mIERyb3Bkb3duTWVudVByaW1pdGl2ZS5JdGVtPixcbiAgUmVhY3QuQ29tcG9uZW50UHJvcHNXaXRob3V0UmVmPHR5cGVvZiBEcm9wZG93bk1lbnVQcmltaXRpdmUuSXRlbT4gJiB7XG4gICAgaW5zZXQ/OiBib29sZWFuXG4gIH1cbj4oKHsgY2xhc3NOYW1lLCBpbnNldCwgLi4ucHJvcHMgfSwgcmVmKSA9PiAoXG4gIDxEcm9wZG93bk1lbnVQcmltaXRpdmUuSXRlbVxuICAgIHJlZj17cmVmfVxuICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICBcInJlbGF0aXZlIGZsZXggY3Vyc29yLWRlZmF1bHQgc2VsZWN0LW5vbmUgaXRlbXMtY2VudGVyIGdhcC0yIHJvdW5kZWQtc20gcHgtMiBweS0xLjUgdGV4dC1zbSBvdXRsaW5lLW5vbmUgdHJhbnNpdGlvbi1jb2xvcnMgZm9jdXM6YmctYWNjZW50IGZvY3VzOnRleHQtYWNjZW50LWZvcmVncm91bmQgZGF0YS1bZGlzYWJsZWRdOnBvaW50ZXItZXZlbnRzLW5vbmUgZGF0YS1bZGlzYWJsZWRdOm9wYWNpdHktNTAgWyZfc3ZnXTpwb2ludGVyLWV2ZW50cy1ub25lIFsmX3N2Z106c2l6ZS00IFsmX3N2Z106c2hyaW5rLTBcIixcbiAgICAgIGluc2V0ICYmIFwicGwtOFwiLFxuICAgICAgY2xhc3NOYW1lXG4gICAgKX1cbiAgICB7Li4ucHJvcHN9XG4gIC8+XG4pKVxuRHJvcGRvd25NZW51SXRlbS5kaXNwbGF5TmFtZSA9IERyb3Bkb3duTWVudVByaW1pdGl2ZS5JdGVtLmRpc3BsYXlOYW1lXG5cbmNvbnN0IERyb3Bkb3duTWVudUNoZWNrYm94SXRlbSA9IFJlYWN0LmZvcndhcmRSZWY8XG4gIFJlYWN0LkVsZW1lbnRSZWY8dHlwZW9mIERyb3Bkb3duTWVudVByaW1pdGl2ZS5DaGVja2JveEl0ZW0+LFxuICBSZWFjdC5Db21wb25lbnRQcm9wc1dpdGhvdXRSZWY8dHlwZW9mIERyb3Bkb3duTWVudVByaW1pdGl2ZS5DaGVja2JveEl0ZW0+XG4+KCh7IGNsYXNzTmFtZSwgY2hpbGRyZW4sIGNoZWNrZWQsIC4uLnByb3BzIH0sIHJlZikgPT4gKFxuICA8RHJvcGRvd25NZW51UHJpbWl0aXZlLkNoZWNrYm94SXRlbVxuICAgIHJlZj17cmVmfVxuICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICBcInJlbGF0aXZlIGZsZXggY3Vyc29yLWRlZmF1bHQgc2VsZWN0LW5vbmUgaXRlbXMtY2VudGVyIHJvdW5kZWQtc20gcHktMS41IHBsLTggcHItMiB0ZXh0LXNtIG91dGxpbmUtbm9uZSB0cmFuc2l0aW9uLWNvbG9ycyBmb2N1czpiZy1hY2NlbnQgZm9jdXM6dGV4dC1hY2NlbnQtZm9yZWdyb3VuZCBkYXRhLVtkaXNhYmxlZF06cG9pbnRlci1ldmVudHMtbm9uZSBkYXRhLVtkaXNhYmxlZF06b3BhY2l0eS01MFwiLFxuICAgICAgY2xhc3NOYW1lXG4gICAgKX1cbiAgICBjaGVja2VkPXtjaGVja2VkfVxuICAgIHsuLi5wcm9wc31cbiAgPlxuICAgIDxzcGFuIGNsYXNzTmFtZT1cImFic29sdXRlIGxlZnQtMiBmbGV4IGgtMy41IHctMy41IGl0ZW1zLWNlbnRlciBqdXN0aWZ5LWNlbnRlclwiPlxuICAgICAgPERyb3Bkb3duTWVudVByaW1pdGl2ZS5JdGVtSW5kaWNhdG9yPlxuICAgICAgICA8Q2hlY2sgY2xhc3NOYW1lPVwiaC00IHctNFwiIC8+XG4gICAgICA8L0Ryb3Bkb3duTWVudVByaW1pdGl2ZS5JdGVtSW5kaWNhdG9yPlxuICAgIDwvc3Bhbj5cbiAgICB7Y2hpbGRyZW59XG4gIDwvRHJvcGRvd25NZW51UHJpbWl0aXZlLkNoZWNrYm94SXRlbT5cbikpXG5Ecm9wZG93bk1lbnVDaGVja2JveEl0ZW0uZGlzcGxheU5hbWUgPVxuICBEcm9wZG93bk1lbnVQcmltaXRpdmUuQ2hlY2tib3hJdGVtLmRpc3BsYXlOYW1lXG5cbmNvbnN0IERyb3Bkb3duTWVudVJhZGlvSXRlbSA9IFJlYWN0LmZvcndhcmRSZWY8XG4gIFJlYWN0LkVsZW1lbnRSZWY8dHlwZW9mIERyb3Bkb3duTWVudVByaW1pdGl2ZS5SYWRpb0l0ZW0+LFxuICBSZWFjdC5Db21wb25lbnRQcm9wc1dpdGhvdXRSZWY8dHlwZW9mIERyb3Bkb3duTWVudVByaW1pdGl2ZS5SYWRpb0l0ZW0+XG4+KCh7IGNsYXNzTmFtZSwgY2hpbGRyZW4sIC4uLnByb3BzIH0sIHJlZikgPT4gKFxuICA8RHJvcGRvd25NZW51UHJpbWl0aXZlLlJhZGlvSXRlbVxuICAgIHJlZj17cmVmfVxuICAgIGNsYXNzTmFtZT17Y24oXG4gICAgICBcInJlbGF0aXZlIGZsZXggY3Vyc29yLWRlZmF1bHQgc2VsZWN0LW5vbmUgaXRlbXMtY2VudGVyIHJvdW5kZWQtc20gcHktMS41IHBsLTggcHItMiB0ZXh0LXNtIG91dGxpbmUtbm9uZSB0cmFuc2l0aW9uLWNvbG9ycyBmb2N1czpiZy1hY2NlbnQgZm9jdXM6dGV4dC1hY2NlbnQtZm9yZWdyb3VuZCBkYXRhLVtkaXNhYmxlZF06cG9pbnRlci1ldmVudHMtbm9uZSBkYXRhLVtkaXNhYmxlZF06b3BhY2l0eS01MFwiLFxuICAgICAgY2xhc3NOYW1lXG4gICAgKX1cbiAgICB7Li4ucHJvcHN9XG4gID5cbiAgICA8c3BhbiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBsZWZ0LTIgZmxleCBoLTMuNSB3LTMuNSBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXJcIj5cbiAgICAgIDxEcm9wZG93bk1lbnVQcmltaXRpdmUuSXRlbUluZGljYXRvcj5cbiAgICAgICAgPENpcmNsZSBjbGFzc05hbWU9XCJoLTIgdy0yIGZpbGwtY3VycmVudFwiIC8+XG4gICAgICA8L0Ryb3Bkb3duTWVudVByaW1pdGl2ZS5JdGVtSW5kaWNhdG9yPlxuICAgIDwvc3Bhbj5cbiAgICB7Y2hpbGRyZW59XG4gIDwvRHJvcGRvd25NZW51UHJpbWl0aXZlLlJhZGlvSXRlbT5cbikpXG5Ecm9wZG93bk1lbnVSYWRpb0l0ZW0uZGlzcGxheU5hbWUgPSBEcm9wZG93bk1lbnVQcmltaXRpdmUuUmFkaW9JdGVtLmRpc3BsYXlOYW1lXG5cbmNvbnN0IERyb3Bkb3duTWVudUxhYmVsID0gUmVhY3QuZm9yd2FyZFJlZjxcbiAgUmVhY3QuRWxlbWVudFJlZjx0eXBlb2YgRHJvcGRvd25NZW51UHJpbWl0aXZlLkxhYmVsPixcbiAgUmVhY3QuQ29tcG9uZW50UHJvcHNXaXRob3V0UmVmPHR5cGVvZiBEcm9wZG93bk1lbnVQcmltaXRpdmUuTGFiZWw+ICYge1xuICAgIGluc2V0PzogYm9vbGVhblxuICB9XG4+KCh7IGNsYXNzTmFtZSwgaW5zZXQsIC4uLnByb3BzIH0sIHJlZikgPT4gKFxuICA8RHJvcGRvd25NZW51UHJpbWl0aXZlLkxhYmVsXG4gICAgcmVmPXtyZWZ9XG4gICAgY2xhc3NOYW1lPXtjbihcbiAgICAgIFwicHgtMiBweS0xLjUgdGV4dC1zbSBmb250LXNlbWlib2xkXCIsXG4gICAgICBpbnNldCAmJiBcInBsLThcIixcbiAgICAgIGNsYXNzTmFtZVxuICAgICl9XG4gICAgey4uLnByb3BzfVxuICAvPlxuKSlcbkRyb3Bkb3duTWVudUxhYmVsLmRpc3BsYXlOYW1lID0gRHJvcGRvd25NZW51UHJpbWl0aXZlLkxhYmVsLmRpc3BsYXlOYW1lXG5cbmNvbnN0IERyb3Bkb3duTWVudVNlcGFyYXRvciA9IFJlYWN0LmZvcndhcmRSZWY8XG4gIFJlYWN0LkVsZW1lbnRSZWY8dHlwZW9mIERyb3Bkb3duTWVudVByaW1pdGl2ZS5TZXBhcmF0b3I+LFxuICBSZWFjdC5Db21wb25lbnRQcm9wc1dpdGhvdXRSZWY8dHlwZW9mIERyb3Bkb3duTWVudVByaW1pdGl2ZS5TZXBhcmF0b3I+XG4+KCh7IGNsYXNzTmFtZSwgLi4ucHJvcHMgfSwgcmVmKSA9PiAoXG4gIDxEcm9wZG93bk1lbnVQcmltaXRpdmUuU2VwYXJhdG9yXG4gICAgcmVmPXtyZWZ9XG4gICAgY2xhc3NOYW1lPXtjbihcIi1teC0xIG15LTEgaC1weCBiZy1tdXRlZFwiLCBjbGFzc05hbWUpfVxuICAgIHsuLi5wcm9wc31cbiAgLz5cbikpXG5Ecm9wZG93bk1lbnVTZXBhcmF0b3IuZGlzcGxheU5hbWUgPSBEcm9wZG93bk1lbnVQcmltaXRpdmUuU2VwYXJhdG9yLmRpc3BsYXlOYW1lXG5cbmNvbnN0IERyb3Bkb3duTWVudVNob3J0Y3V0ID0gKHtcbiAgY2xhc3NOYW1lLFxuICAuLi5wcm9wc1xufTogUmVhY3QuSFRNTEF0dHJpYnV0ZXM8SFRNTFNwYW5FbGVtZW50PikgPT4ge1xuICByZXR1cm4gKFxuICAgIDxzcGFuXG4gICAgICBjbGFzc05hbWU9e2NuKFwibWwtYXV0byB0ZXh0LXhzIHRyYWNraW5nLXdpZGVzdCBvcGFjaXR5LTYwXCIsIGNsYXNzTmFtZSl9XG4gICAgICB7Li4ucHJvcHN9XG4gICAgLz5cbiAgKVxufVxuRHJvcGRvd25NZW51U2hvcnRjdXQuZGlzcGxheU5hbWUgPSBcIkRyb3Bkb3duTWVudVNob3J0Y3V0XCJcblxuZXhwb3J0IHtcbiAgRHJvcGRvd25NZW51LFxuICBEcm9wZG93bk1lbnVUcmlnZ2VyLFxuICBEcm9wZG93bk1lbnVDb250ZW50LFxuICBEcm9wZG93bk1lbnVJdGVtLFxuICBEcm9wZG93bk1lbnVDaGVja2JveEl0ZW0sXG4gIERyb3Bkb3duTWVudVJhZGlvSXRlbSxcbiAgRHJvcGRvd25NZW51TGFiZWwsXG4gIERyb3Bkb3duTWVudVNlcGFyYXRvcixcbiAgRHJvcGRvd25NZW51U2hvcnRjdXQsXG4gIERyb3Bkb3duTWVudUdyb3VwLFxuICBEcm9wZG93bk1lbnVQb3J0YWwsXG4gIERyb3Bkb3duTWVudVN1YixcbiAgRHJvcGRvd25NZW51U3ViQ29udGVudCxcbiAgRHJvcGRvd25NZW51U3ViVHJpZ2dlcixcbiAgRHJvcGRvd25NZW51UmFkaW9Hcm91cCxcbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsIkRyb3Bkb3duTWVudVByaW1pdGl2ZSIsIkNoZWNrIiwiQ2hldnJvblJpZ2h0IiwiQ2lyY2xlIiwiY24iLCJEcm9wZG93bk1lbnUiLCJSb290IiwiRHJvcGRvd25NZW51VHJpZ2dlciIsIlRyaWdnZXIiLCJEcm9wZG93bk1lbnVHcm91cCIsIkdyb3VwIiwiRHJvcGRvd25NZW51UG9ydGFsIiwiUG9ydGFsIiwiRHJvcGRvd25NZW51U3ViIiwiU3ViIiwiRHJvcGRvd25NZW51UmFkaW9Hcm91cCIsIlJhZGlvR3JvdXAiLCJEcm9wZG93bk1lbnVTdWJUcmlnZ2VyIiwiZm9yd2FyZFJlZiIsImNsYXNzTmFtZSIsImluc2V0IiwiY2hpbGRyZW4iLCJwcm9wcyIsInJlZiIsIlN1YlRyaWdnZXIiLCJkaXNwbGF5TmFtZSIsIkRyb3Bkb3duTWVudVN1YkNvbnRlbnQiLCJTdWJDb250ZW50IiwiRHJvcGRvd25NZW51Q29udGVudCIsInNpZGVPZmZzZXQiLCJDb250ZW50IiwiRHJvcGRvd25NZW51SXRlbSIsIkl0ZW0iLCJEcm9wZG93bk1lbnVDaGVja2JveEl0ZW0iLCJjaGVja2VkIiwiQ2hlY2tib3hJdGVtIiwic3BhbiIsIkl0ZW1JbmRpY2F0b3IiLCJEcm9wZG93bk1lbnVSYWRpb0l0ZW0iLCJSYWRpb0l0ZW0iLCJEcm9wZG93bk1lbnVMYWJlbCIsIkxhYmVsIiwiRHJvcGRvd25NZW51U2VwYXJhdG9yIiwiU2VwYXJhdG9yIiwiRHJvcGRvd25NZW51U2hvcnRjdXQiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/dropdown-menu.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/input.tsx":
/*!*********************************!*\
  !*** ./components/ui/input.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Input: () => (/* binding */ Input)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n\n\n\nconst Input = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, type, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n        type: type,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\", className),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\ui\\\\input.tsx\",\n        lineNumber: 8,\n        columnNumber: 7\n    }, undefined);\n});\nInput.displayName = \"Input\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL2lucHV0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQThCO0FBRUU7QUFFaEMsTUFBTUUsc0JBQVFGLDZDQUFnQixDQUM1QixDQUFDLEVBQUVJLFNBQVMsRUFBRUMsSUFBSSxFQUFFLEdBQUdDLE9BQU8sRUFBRUM7SUFDOUIscUJBQ0UsOERBQUNDO1FBQ0NILE1BQU1BO1FBQ05ELFdBQVdILDhDQUFFQSxDQUNYLGtZQUNBRztRQUVGRyxLQUFLQTtRQUNKLEdBQUdELEtBQUs7Ozs7OztBQUdmO0FBRUZKLE1BQU1PLFdBQVcsR0FBRztBQUVKIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbXktdjAtcHJvamVjdC8uL2NvbXBvbmVudHMvdWkvaW5wdXQudHN4P2RhNzkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCJcblxuaW1wb3J0IHsgY24gfSBmcm9tIFwiQC9saWIvdXRpbHNcIlxuXG5jb25zdCBJbnB1dCA9IFJlYWN0LmZvcndhcmRSZWY8SFRNTElucHV0RWxlbWVudCwgUmVhY3QuQ29tcG9uZW50UHJvcHM8XCJpbnB1dFwiPj4oXG4gICh7IGNsYXNzTmFtZSwgdHlwZSwgLi4ucHJvcHMgfSwgcmVmKSA9PiB7XG4gICAgcmV0dXJuIChcbiAgICAgIDxpbnB1dFxuICAgICAgICB0eXBlPXt0eXBlfVxuICAgICAgICBjbGFzc05hbWU9e2NuKFxuICAgICAgICAgIFwiZmxleCBoLTEwIHctZnVsbCByb3VuZGVkLW1kIGJvcmRlciBib3JkZXItaW5wdXQgYmctYmFja2dyb3VuZCBweC0zIHB5LTIgdGV4dC1iYXNlIHJpbmctb2Zmc2V0LWJhY2tncm91bmQgZmlsZTpib3JkZXItMCBmaWxlOmJnLXRyYW5zcGFyZW50IGZpbGU6dGV4dC1zbSBmaWxlOmZvbnQtbWVkaXVtIGZpbGU6dGV4dC1mb3JlZ3JvdW5kIHBsYWNlaG9sZGVyOnRleHQtbXV0ZWQtZm9yZWdyb3VuZCBmb2N1cy12aXNpYmxlOm91dGxpbmUtbm9uZSBmb2N1cy12aXNpYmxlOnJpbmctMiBmb2N1cy12aXNpYmxlOnJpbmctcmluZyBmb2N1cy12aXNpYmxlOnJpbmctb2Zmc2V0LTIgZGlzYWJsZWQ6Y3Vyc29yLW5vdC1hbGxvd2VkIGRpc2FibGVkOm9wYWNpdHktNTAgbWQ6dGV4dC1zbVwiLFxuICAgICAgICAgIGNsYXNzTmFtZVxuICAgICAgICApfVxuICAgICAgICByZWY9e3JlZn1cbiAgICAgICAgey4uLnByb3BzfVxuICAgICAgLz5cbiAgICApXG4gIH1cbilcbklucHV0LmRpc3BsYXlOYW1lID0gXCJJbnB1dFwiXG5cbmV4cG9ydCB7IElucHV0IH1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsImNuIiwiSW5wdXQiLCJmb3J3YXJkUmVmIiwiY2xhc3NOYW1lIiwidHlwZSIsInByb3BzIiwicmVmIiwiaW5wdXQiLCJkaXNwbGF5TmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/input.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/label.tsx":
/*!*********************************!*\
  !*** ./components/ui/label.tsx ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Label: () => (/* binding */ Label)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_label__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-label */ \"(ssr)/./node_modules/@radix-ui/react-label/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(ssr)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Label auto */ \n\n\n\n\nconst labelVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\");\nconst Label = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_label__WEBPACK_IMPORTED_MODULE_4__.Root, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(labelVariants(), className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\ui\\\\label.tsx\",\n        lineNumber: 18,\n        columnNumber: 3\n    }, undefined));\nLabel.displayName = _radix_ui_react_label__WEBPACK_IMPORTED_MODULE_4__.Root.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3VpL2xhYmVsLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFFOEI7QUFDeUI7QUFDVTtBQUVqQztBQUVoQyxNQUFNSSxnQkFBZ0JGLDZEQUFHQSxDQUN2QjtBQUdGLE1BQU1HLHNCQUFRTCw2Q0FBZ0IsQ0FJNUIsQ0FBQyxFQUFFTyxTQUFTLEVBQUUsR0FBR0MsT0FBTyxFQUFFQyxvQkFDMUIsOERBQUNSLHVEQUFtQjtRQUNsQlEsS0FBS0E7UUFDTEYsV0FBV0osOENBQUVBLENBQUNDLGlCQUFpQkc7UUFDOUIsR0FBR0MsS0FBSzs7Ozs7O0FBR2JILE1BQU1NLFdBQVcsR0FBR1YsdURBQW1CLENBQUNVLFdBQVc7QUFFbkMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9teS12MC1wcm9qZWN0Ly4vY29tcG9uZW50cy91aS9sYWJlbC50c3g/ODhlZCJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIlxuXG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIlxuaW1wb3J0ICogYXMgTGFiZWxQcmltaXRpdmUgZnJvbSBcIkByYWRpeC11aS9yZWFjdC1sYWJlbFwiXG5pbXBvcnQgeyBjdmEsIHR5cGUgVmFyaWFudFByb3BzIH0gZnJvbSBcImNsYXNzLXZhcmlhbmNlLWF1dGhvcml0eVwiXG5cbmltcG9ydCB7IGNuIH0gZnJvbSBcIkAvbGliL3V0aWxzXCJcblxuY29uc3QgbGFiZWxWYXJpYW50cyA9IGN2YShcbiAgXCJ0ZXh0LXNtIGZvbnQtbWVkaXVtIGxlYWRpbmctbm9uZSBwZWVyLWRpc2FibGVkOmN1cnNvci1ub3QtYWxsb3dlZCBwZWVyLWRpc2FibGVkOm9wYWNpdHktNzBcIlxuKVxuXG5jb25zdCBMYWJlbCA9IFJlYWN0LmZvcndhcmRSZWY8XG4gIFJlYWN0LkVsZW1lbnRSZWY8dHlwZW9mIExhYmVsUHJpbWl0aXZlLlJvb3Q+LFxuICBSZWFjdC5Db21wb25lbnRQcm9wc1dpdGhvdXRSZWY8dHlwZW9mIExhYmVsUHJpbWl0aXZlLlJvb3Q+ICZcbiAgICBWYXJpYW50UHJvcHM8dHlwZW9mIGxhYmVsVmFyaWFudHM+XG4+KCh7IGNsYXNzTmFtZSwgLi4ucHJvcHMgfSwgcmVmKSA9PiAoXG4gIDxMYWJlbFByaW1pdGl2ZS5Sb290XG4gICAgcmVmPXtyZWZ9XG4gICAgY2xhc3NOYW1lPXtjbihsYWJlbFZhcmlhbnRzKCksIGNsYXNzTmFtZSl9XG4gICAgey4uLnByb3BzfVxuICAvPlxuKSlcbkxhYmVsLmRpc3BsYXlOYW1lID0gTGFiZWxQcmltaXRpdmUuUm9vdC5kaXNwbGF5TmFtZVxuXG5leHBvcnQgeyBMYWJlbCB9XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJMYWJlbFByaW1pdGl2ZSIsImN2YSIsImNuIiwibGFiZWxWYXJpYW50cyIsIkxhYmVsIiwiZm9yd2FyZFJlZiIsImNsYXNzTmFtZSIsInByb3BzIiwicmVmIiwiUm9vdCIsImRpc3BsYXlOYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/label.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/slider.tsx":
/*!**********************************!*\
  !*** ./components/ui/slider.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Slider: () => (/* binding */ Slider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-slider */ \"(ssr)/./node_modules/@radix-ui/react-slider/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Slider auto */ \n\n\n\nconst Slider = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_slider__WEBPACK_IMPORTED_MODULE_3__.Root, {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"relative flex w-full touch-none select-none items-center\", className),\n        ...props,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_slider__WEBPACK_IMPORTED_MODULE_3__.Track, {\n                className: \"relative h-2 w-full grow overflow-hidden rounded-full bg-secondary\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_slider__WEBPACK_IMPORTED_MODULE_3__.Range, {\n                    className: \"absolute h-full bg-primary\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\ui\\\\slider.tsx\",\n                    lineNumber: 21,\n                    columnNumber: 7\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\ui\\\\slider.tsx\",\n                lineNumber: 20,\n                columnNumber: 5\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_slider__WEBPACK_IMPORTED_MODULE_3__.Thumb, {\n                className: \"block h-5 w-5 rounded-full border-2 border-primary bg-background ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\ui\\\\slider.tsx\",\n                lineNumber: 23,\n                columnNumber: 5\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\ui\\\\slider.tsx\",\n        lineNumber: 12,\n        columnNumber: 3\n    }, undefined));\nSlider.displayName = _radix_ui_react_slider__WEBPACK_IMPORTED_MODULE_3__.Root.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/slider.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/switch.tsx":
/*!**********************************!*\
  !*** ./components/ui/switch.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Switch: () => (/* binding */ Switch)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_switch__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-switch */ \"(ssr)/./node_modules/@radix-ui/react-switch/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Switch auto */ \n\n\n\nconst Switch = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_switch__WEBPACK_IMPORTED_MODULE_3__.Root, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input\", className),\n        ...props,\n        ref: ref,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_switch__WEBPACK_IMPORTED_MODULE_3__.Thumb, {\n            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0\")\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\ui\\\\switch.tsx\",\n            lineNumber: 20,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\ui\\\\switch.tsx\",\n        lineNumber: 12,\n        columnNumber: 3\n    }, undefined));\nSwitch.displayName = _radix_ui_react_switch__WEBPACK_IMPORTED_MODULE_3__.Root.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/switch.tsx\n");

/***/ }),

/***/ "(ssr)/./components/ui/tooltip.tsx":
/*!***********************************!*\
  !*** ./components/ui/tooltip.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Tooltip: () => (/* binding */ Tooltip),\n/* harmony export */   TooltipContent: () => (/* binding */ TooltipContent),\n/* harmony export */   TooltipProvider: () => (/* binding */ TooltipProvider),\n/* harmony export */   TooltipTrigger: () => (/* binding */ TooltipTrigger)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_tooltip__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @radix-ui/react-tooltip */ \"(ssr)/./node_modules/@radix-ui/react-tooltip/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(ssr)/./lib/utils.ts\");\n/* __next_internal_client_entry_do_not_use__ Tooltip,TooltipTrigger,TooltipContent,TooltipProvider auto */ \n\n\n\nconst TooltipProvider = _radix_ui_react_tooltip__WEBPACK_IMPORTED_MODULE_3__.Provider;\nconst Tooltip = _radix_ui_react_tooltip__WEBPACK_IMPORTED_MODULE_3__.Root;\nconst TooltipTrigger = _radix_ui_react_tooltip__WEBPACK_IMPORTED_MODULE_3__.Trigger;\nconst TooltipContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, sideOffset = 4, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_radix_ui_react_tooltip__WEBPACK_IMPORTED_MODULE_3__.Content, {\n        ref: ref,\n        sideOffset: sideOffset,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\components\\\\ui\\\\tooltip.tsx\",\n        lineNumber: 18,\n        columnNumber: 3\n    }, undefined));\nTooltipContent.displayName = _radix_ui_react_tooltip__WEBPACK_IMPORTED_MODULE_3__.Content.displayName;\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./components/ui/tooltip.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/store.ts":
/*!**********************!*\
  !*** ./lib/store.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useUserStore: () => (/* binding */ useUserStore)\n/* harmony export */ });\n/* harmony import */ var zustand__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! zustand */ \"(ssr)/./node_modules/zustand/esm/react.mjs\");\n/* harmony import */ var zustand_middleware__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zustand/middleware */ \"(ssr)/./node_modules/zustand/esm/middleware.mjs\");\n\n\nconst useUserStore = (0,zustand__WEBPACK_IMPORTED_MODULE_0__.create)()((0,zustand_middleware__WEBPACK_IMPORTED_MODULE_1__.persist)((set)=>({\n        user: null,\n        login: (user)=>set({\n                user\n            }),\n        logout: ()=>set({\n                user: null\n            }),\n        updateUser: (user)=>set({\n                user\n            })\n    }), {\n    name: \"clipsnip-user-storage\"\n}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvc3RvcmUudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQWdDO0FBQ1k7QUFnQnJDLE1BQU1FLGVBQWVGLCtDQUFNQSxHQUNoQ0MsMkRBQU9BLENBQ0wsQ0FBQ0UsTUFBUztRQUNSQyxNQUFNO1FBQ05DLE9BQU8sQ0FBQ0QsT0FBU0QsSUFBSTtnQkFBRUM7WUFBSztRQUM1QkUsUUFBUSxJQUFNSCxJQUFJO2dCQUFFQyxNQUFNO1lBQUs7UUFDL0JHLFlBQVksQ0FBQ0gsT0FBU0QsSUFBSTtnQkFBRUM7WUFBSztJQUNuQyxJQUNBO0lBQ0VJLE1BQU07QUFDUixJQUVIIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbXktdjAtcHJvamVjdC8uL2xpYi9zdG9yZS50cz9lODQyIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNyZWF0ZSB9IGZyb20gXCJ6dXN0YW5kXCJcbmltcG9ydCB7IHBlcnNpc3QgfSBmcm9tIFwienVzdGFuZC9taWRkbGV3YXJlXCJcblxudHlwZSBVc2VyID0ge1xuICBpZDogc3RyaW5nXG4gIG5hbWU6IHN0cmluZ1xuICBlbWFpbDogc3RyaW5nXG4gIHBsYW46IFwiZnJlZVwiIHwgXCJwcm9cIiB8IFwiY2xpcHBlclwiXG59XG5cbnR5cGUgVXNlclN0b3JlID0ge1xuICB1c2VyOiBVc2VyIHwgbnVsbFxuICBsb2dpbjogKHVzZXI6IFVzZXIpID0+IHZvaWRcbiAgbG9nb3V0OiAoKSA9PiB2b2lkXG4gIHVwZGF0ZVVzZXI6ICh1c2VyOiBVc2VyKSA9PiB2b2lkXG59XG5cbmV4cG9ydCBjb25zdCB1c2VVc2VyU3RvcmUgPSBjcmVhdGU8VXNlclN0b3JlPigpKFxuICBwZXJzaXN0KFxuICAgIChzZXQpID0+ICh7XG4gICAgICB1c2VyOiBudWxsLFxuICAgICAgbG9naW46ICh1c2VyKSA9PiBzZXQoeyB1c2VyIH0pLFxuICAgICAgbG9nb3V0OiAoKSA9PiBzZXQoeyB1c2VyOiBudWxsIH0pLFxuICAgICAgdXBkYXRlVXNlcjogKHVzZXIpID0+IHNldCh7IHVzZXIgfSksXG4gICAgfSksXG4gICAge1xuICAgICAgbmFtZTogXCJjbGlwc25pcC11c2VyLXN0b3JhZ2VcIixcbiAgICB9LFxuICApLFxuKVxuIl0sIm5hbWVzIjpbImNyZWF0ZSIsInBlcnNpc3QiLCJ1c2VVc2VyU3RvcmUiLCJzZXQiLCJ1c2VyIiwibG9naW4iLCJsb2dvdXQiLCJ1cGRhdGVVc2VyIiwibmFtZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./lib/store.ts\n");

/***/ }),

/***/ "(ssr)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(ssr)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(ssr)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9saWIvdXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTRDO0FBQ0o7QUFFakMsU0FBU0UsR0FBRyxHQUFHQyxNQUFvQjtJQUN4QyxPQUFPRix1REFBT0EsQ0FBQ0QsMENBQUlBLENBQUNHO0FBQ3RCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vbXktdjAtcHJvamVjdC8uL2xpYi91dGlscy50cz9mNzQ1Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGNsc3gsIHR5cGUgQ2xhc3NWYWx1ZSB9IGZyb20gXCJjbHN4XCJcbmltcG9ydCB7IHR3TWVyZ2UgfSBmcm9tIFwidGFpbHdpbmQtbWVyZ2VcIlxuXG5leHBvcnQgZnVuY3Rpb24gY24oLi4uaW5wdXRzOiBDbGFzc1ZhbHVlW10pIHtcbiAgcmV0dXJuIHR3TWVyZ2UoY2xzeChpbnB1dHMpKVxufVxuIl0sIm5hbWVzIjpbImNsc3giLCJ0d01lcmdlIiwiY24iLCJpbnB1dHMiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"be2c847e97cf\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9teS12MC1wcm9qZWN0Ly4vYXBwL2dsb2JhbHMuY3NzP2MzZDciXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJiZTJjODQ3ZTk3Y2ZcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/dashboard/layout.tsx":
/*!**********************************!*\
  !*** ./app/dashboard/layout.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DashboardLayout)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _components_dashboard_dashboard_nav__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/dashboard/dashboard-nav */ \"(rsc)/./components/dashboard/dashboard-nav.tsx\");\n/* harmony import */ var _components_dashboard_dashboard_header__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/dashboard/dashboard-header */ \"(rsc)/./components/dashboard/dashboard-header.tsx\");\n\n\n\nfunction DashboardLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen bg-gradient-to-br from-luxury-gold/5 via-white to-luxury-green/5\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_dashboard_nav__WEBPACK_IMPORTED_MODULE_1__.DashboardNav, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\layout.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col flex-1 overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_dashboard_dashboard_header__WEBPACK_IMPORTED_MODULE_2__.DashboardHeader, {}, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\layout.tsx\",\n                        lineNumber: 14,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"flex-1 overflow-y-auto p-6 bg-gradient-to-br from-luxury-gold/3 to-luxury-green/3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 opacity-5\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute inset-0 bg-gradient-to-r from-luxury-green via-luxury-gold to-luxury-green bg-[length:200%_200%] animate-pulse\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\layout.tsx\",\n                                        lineNumber: 19,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\layout.tsx\",\n                                    lineNumber: 18,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative z-10\",\n                                    children: children\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\layout.tsx\",\n                                    lineNumber: 21,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\layout.tsx\",\n                            lineNumber: 16,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\layout.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\layout.tsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\dashboard\\\\layout.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZGFzaGJvYXJkL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQ21FO0FBQ007QUFFMUQsU0FBU0UsZ0JBQWdCLEVBQ3RDQyxRQUFRLEVBR1Q7SUFDQyxxQkFDRSw4REFBQ0M7UUFBSUMsV0FBVTs7MEJBQ2IsOERBQUNMLDZFQUFZQTs7Ozs7MEJBQ2IsOERBQUNJO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ0osbUZBQWVBOzs7OztrQ0FDaEIsOERBQUNLO3dCQUFLRCxXQUFVO2tDQUNkLDRFQUFDRDs0QkFBSUMsV0FBVTs7OENBRWIsOERBQUNEO29DQUFJQyxXQUFVOzhDQUNiLDRFQUFDRDt3Q0FBSUMsV0FBVTs7Ozs7Ozs7Ozs7OENBRWpCLDhEQUFDRDtvQ0FBSUMsV0FBVTs4Q0FBaUJGOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU01QyIsInNvdXJjZXMiOlsid2VicGFjazovL215LXYwLXByb2plY3QvLi9hcHAvZGFzaGJvYXJkL2xheW91dC50c3g/NzFlNiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgdHlwZSB7IFJlYWN0Tm9kZSB9IGZyb20gXCJyZWFjdFwiXG5pbXBvcnQgeyBEYXNoYm9hcmROYXYgfSBmcm9tIFwiQC9jb21wb25lbnRzL2Rhc2hib2FyZC9kYXNoYm9hcmQtbmF2XCJcbmltcG9ydCB7IERhc2hib2FyZEhlYWRlciB9IGZyb20gXCJAL2NvbXBvbmVudHMvZGFzaGJvYXJkL2Rhc2hib2FyZC1oZWFkZXJcIlxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBEYXNoYm9hcmRMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0Tm9kZVxufSkge1xuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBoLXNjcmVlbiBiZy1ncmFkaWVudC10by1iciBmcm9tLWx1eHVyeS1nb2xkLzUgdmlhLXdoaXRlIHRvLWx1eHVyeS1ncmVlbi81XCI+XG4gICAgICA8RGFzaGJvYXJkTmF2IC8+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggZmxleC1jb2wgZmxleC0xIG92ZXJmbG93LWhpZGRlblwiPlxuICAgICAgICA8RGFzaGJvYXJkSGVhZGVyIC8+XG4gICAgICAgIDxtYWluIGNsYXNzTmFtZT1cImZsZXgtMSBvdmVyZmxvdy15LWF1dG8gcC02IGJnLWdyYWRpZW50LXRvLWJyIGZyb20tbHV4dXJ5LWdvbGQvMyB0by1sdXh1cnktZ3JlZW4vM1wiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmVcIj5cbiAgICAgICAgICAgIHsvKiBTdWJ0bGUgYmFja2dyb3VuZCBwYXR0ZXJuICovfVxuICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJhYnNvbHV0ZSBpbnNldC0wIG9wYWNpdHktNVwiPlxuICAgICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImFic29sdXRlIGluc2V0LTAgYmctZ3JhZGllbnQtdG8tciBmcm9tLWx1eHVyeS1ncmVlbiB2aWEtbHV4dXJ5LWdvbGQgdG8tbHV4dXJ5LWdyZWVuIGJnLVtsZW5ndGg6MjAwJV8yMDAlXSBhbmltYXRlLXB1bHNlXCI+PC9kaXY+XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmUgei0xMFwiPntjaGlsZHJlbn08L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9tYWluPlxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJEYXNoYm9hcmROYXYiLCJEYXNoYm9hcmRIZWFkZXIiLCJEYXNoYm9hcmRMYXlvdXQiLCJjaGlsZHJlbiIsImRpdiIsImNsYXNzTmFtZSIsIm1haW4iXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/dashboard/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/dashboard/page.tsx":
/*!********************************!*\
  !*** ./app/dashboard/page.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\Clipsnip_ai\app\dashboard\page.tsx#default`));


/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _components_theme_provider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/theme-provider */ \"(rsc)/./components/theme-provider.tsx\");\n\n\n\n\nconst metadata = {\n    title: \"ClipSnip - Auto-create viral clips in one click\",\n    description: \"Turn any video into a viral short-form clip with automatic cropping and subtitles.\",\n    generator: \"v0.dev\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: (next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().className),\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_theme_provider__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n                attribute: \"class\",\n                defaultTheme: \"light\",\n                enableSystem: true,\n                disableTransitionOnChange: true,\n                children: children\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\layout.tsx\",\n                lineNumber: 23,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\layout.tsx\",\n            lineNumber: 22,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\layout.tsx\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./components/dashboard/dashboard-header.tsx":
/*!***************************************************!*\
  !*** ./components/dashboard/dashboard-header.tsx ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   DashboardHeader: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\Clipsnip_ai\components\dashboard\dashboard-header.tsx#DashboardHeader`);


/***/ }),

/***/ "(rsc)/./components/dashboard/dashboard-nav.tsx":
/*!************************************************!*\
  !*** ./components/dashboard/dashboard-nav.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   DashboardNav: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\Clipsnip_ai\components\dashboard\dashboard-nav.tsx#DashboardNav`);


/***/ }),

/***/ "(rsc)/./components/theme-provider.tsx":
/*!***************************************!*\
  !*** ./components/theme-provider.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThemeProvider: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`C:\Users\<USER>\Documents\augment-projects\Clipsnip_ai\components\theme-provider.tsx#ThemeProvider`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@radix-ui","vendor-chunks/tailwind-merge","vendor-chunks/lucide-react","vendor-chunks/next-themes","vendor-chunks/class-variance-authority","vendor-chunks/@swc","vendor-chunks/clsx","vendor-chunks/framer-motion","vendor-chunks/motion-dom","vendor-chunks/zustand","vendor-chunks/motion-utils","vendor-chunks/react-remove-scroll","vendor-chunks/@floating-ui","vendor-chunks/react-style-singleton","vendor-chunks/react-remove-scroll-bar","vendor-chunks/use-callback-ref","vendor-chunks/use-sidecar","vendor-chunks/tslib","vendor-chunks/get-nonce","vendor-chunks/aria-hidden"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fdashboard%2Fpage&page=%2Fdashboard%2Fpage&appPaths=%2Fdashboard%2Fpage&pagePath=private-next-app-dir%2Fdashboard%2Fpage.tsx&appDir=C%3A%5CUsers%5CDubyRodman%5CDocuments%5Caugment-projects%5CClipsnip_ai%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5CDubyRodman%5CDocuments%5Caugment-projects%5CClipsnip_ai&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();