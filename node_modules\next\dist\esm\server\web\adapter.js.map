{"version": 3, "sources": ["../../../src/server/web/adapter.ts"], "names": ["PageSignatureError", "fromNodeOutgoingHttpHeaders", "NextFetchEvent", "NextRequest", "NextResponse", "relativizeURL", "waitUntilSymbol", "NextURL", "stripInternalSearchParams", "normalizeRscURL", "FLIGHT_PARAMETERS", "NEXT_QUERY_PARAM_PREFIX", "ensureInstrumentationRegistered", "RequestAsyncStorageWrapper", "requestAsyncStorage", "getTracer", "MiddlewareSpan", "getEdgePreviewProps", "NextRequestHint", "constructor", "params", "input", "init", "sourcePage", "page", "request", "respondWith", "waitUntil", "headersGetter", "keys", "headers", "Array", "from", "get", "key", "undefined", "propagator", "fn", "tracer", "withPropagatedContext", "testApisIntercepted", "ensureTestApisIntercepted", "process", "env", "NEXT_PRIVATE_TEST_PROXY", "interceptTestApis", "wrapRequestHandler", "require", "adapter", "isEdgeRendering", "self", "__BUILD_MANIFEST", "url", "requestUrl", "nextConfig", "searchParams", "value", "getAll", "startsWith", "normalizedKey", "substring", "length", "delete", "val", "append", "buildId", "isNextDataRequest", "pathname", "requestHeaders", "flightHeaders", "Map", "param", "toString", "toLowerCase", "set", "normalizeUrl", "__NEXT_NO_MIDDLEWARE_URL_NORMALIZE", "URL", "body", "geo", "ip", "method", "signal", "Object", "defineProperty", "enumerable", "globalThis", "__incrementalCache", "IncrementalCache", "appDir", "fetchCache", "minimalMode", "NODE_ENV", "fetchCacheKeyPrefix", "__NEXT_FETCH_CACHE_KEY_PREFIX", "dev", "requestProtocol", "getPrerenderManifest", "version", "routes", "dynamicRoutes", "notFoundRoutes", "preview", "event", "response", "cookiesFromResponse", "isMiddleware", "trace", "execute", "spanName", "nextUrl", "attributes", "wrap", "req", "renderOpts", "onUpdateCookies", "cookies", "previewProps", "handler", "Response", "TypeError", "rewrite", "rewriteUrl", "forceLocale", "host", "String", "relativizedRewrite", "__NEXT_EXTERNAL_MIDDLEWARE_REWRITE_RESOLVE", "match", "redirect", "redirectURL", "finalResponse", "next", "middlewareOverrideHeaders", "overwrittenHeaders", "push", "join", "Promise", "all", "fetchMetrics"], "mappings": "AAEA,SAASA,kBAAkB,QAAQ,UAAS;AAC5C,SAASC,2BAA2B,QAAQ,UAAS;AACrD,SAASC,cAAc,QAAQ,+BAA8B;AAC7D,SAASC,WAAW,QAAQ,2BAA0B;AACtD,SAASC,YAAY,QAAQ,4BAA2B;AACxD,SAASC,aAAa,QAAQ,+CAA8C;AAC5E,SAASC,eAAe,QAAQ,+BAA8B;AAC9D,SAASC,OAAO,QAAQ,aAAY;AACpC,SAASC,yBAAyB,QAAQ,oBAAmB;AAC7D,SAASC,eAAe,QAAQ,0CAAyC;AACzE,SAASC,iBAAiB,QAAQ,6CAA4C;AAC9E,SAASC,uBAAuB,QAAQ,sBAAqB;AAC7D,SAASC,+BAA+B,QAAQ,YAAW;AAC3D,SAASC,0BAA0B,QAAQ,iDAAgD;AAC3F,SAASC,mBAAmB,QAAQ,yDAAwD;AAC5F,SAASC,SAAS,QAAQ,sBAAqB;AAE/C,SAASC,cAAc,QAAQ,yBAAwB;AACvD,SAASC,mBAAmB,QAAQ,2BAA0B;AAE9D,OAAO,MAAMC,wBAAwBf;IAInCgB,YAAYC,MAIX,CAAE;QACD,KAAK,CAACA,OAAOC,KAAK,EAAED,OAAOE,IAAI;QAC/B,IAAI,CAACC,UAAU,GAAGH,OAAOI,IAAI;IAC/B;IAEA,IAAIC,UAAU;QACZ,MAAM,IAAIzB,mBAAmB;YAAEwB,MAAM,IAAI,CAACD,UAAU;QAAC;IACvD;IAEAG,cAAc;QACZ,MAAM,IAAI1B,mBAAmB;YAAEwB,MAAM,IAAI,CAACD,UAAU;QAAC;IACvD;IAEAI,YAAY;QACV,MAAM,IAAI3B,mBAAmB;YAAEwB,MAAM,IAAI,CAACD,UAAU;QAAC;IACvD;AACF;AAEA,MAAMK,gBAAwC;IAC5CC,MAAM,CAACC,UAAYC,MAAMC,IAAI,CAACF,QAAQD,IAAI;IAC1CI,KAAK,CAACH,SAASI,MAAQJ,QAAQG,GAAG,CAACC,QAAQC;AAC7C;AASA,IAAIC,aAA8D,CAChEX,SACAY;IAEA,MAAMC,SAASvB;IACf,OAAOuB,OAAOC,qBAAqB,CAACd,QAAQK,OAAO,EAAEO,IAAIT;AAC3D;AAEA,IAAIY,sBAAsB;AAE1B,SAASC;IACP,IAAI,CAACD,qBAAqB;QACxBA,sBAAsB;QACtB,IAAIE,QAAQC,GAAG,CAACC,uBAAuB,KAAK,QAAQ;YAClD,MAAM,EACJC,iBAAiB,EACjBC,kBAAkB,EACnB,GAAGC,QAAQ;YACZF;YACAT,aAAaU,mBAAmBV;QAClC;IACF;AACF;AAEA,OAAO,eAAeY,QACpB5B,MAAsB;IAEtBqB;IACA,MAAM7B;IAEN,yCAAyC;IACzC,MAAMqC,kBAAkB,OAAOC,KAAKC,gBAAgB,KAAK;IAEzD/B,OAAOK,OAAO,CAAC2B,GAAG,GAAG3C,gBAAgBW,OAAOK,OAAO,CAAC2B,GAAG;IAEvD,MAAMC,aAAa,IAAI9C,QAAQa,OAAOK,OAAO,CAAC2B,GAAG,EAAE;QACjDtB,SAASV,OAAOK,OAAO,CAACK,OAAO;QAC/BwB,YAAYlC,OAAOK,OAAO,CAAC6B,UAAU;IACvC;IAEA,yIAAyI;IACzI,4CAA4C;IAC5C,MAAMzB,OAAO;WAAIwB,WAAWE,YAAY,CAAC1B,IAAI;KAAG;IAChD,KAAK,MAAMK,OAAOL,KAAM;QACtB,MAAM2B,QAAQH,WAAWE,YAAY,CAACE,MAAM,CAACvB;QAE7C,IACEA,QAAQvB,2BACRuB,IAAIwB,UAAU,CAAC/C,0BACf;YACA,MAAMgD,gBAAgBzB,IAAI0B,SAAS,CAACjD,wBAAwBkD,MAAM;YAClER,WAAWE,YAAY,CAACO,MAAM,CAACH;YAE/B,KAAK,MAAMI,OAAOP,MAAO;gBACvBH,WAAWE,YAAY,CAACS,MAAM,CAACL,eAAeI;YAChD;YACAV,WAAWE,YAAY,CAACO,MAAM,CAAC5B;QACjC;IACF;IAEA,4DAA4D;IAC5D,MAAM+B,UAAUZ,WAAWY,OAAO;IAClCZ,WAAWY,OAAO,GAAG;IAErB,MAAMC,oBAAoB9C,OAAOK,OAAO,CAACK,OAAO,CAAC,gBAAgB;IAEjE,IAAIoC,qBAAqBb,WAAWc,QAAQ,KAAK,UAAU;QACzDd,WAAWc,QAAQ,GAAG;IACxB;IAEA,MAAMC,iBAAiBnE,4BAA4BmB,OAAOK,OAAO,CAACK,OAAO;IACzE,MAAMuC,gBAAgB,IAAIC;IAC1B,oDAAoD;IACpD,IAAI,CAACrB,iBAAiB;QACpB,KAAK,MAAMsB,SAAS7D,kBAAmB;YACrC,MAAMwB,MAAMqC,MAAMC,QAAQ,GAAGC,WAAW;YACxC,MAAMjB,QAAQY,eAAenC,GAAG,CAACC;YACjC,IAAIsB,OAAO;gBACTa,cAAcK,GAAG,CAACxC,KAAKkC,eAAenC,GAAG,CAACC;gBAC1CkC,eAAeN,MAAM,CAAC5B;YACxB;QACF;IACF;IAEA,MAAMyC,eAAejC,QAAQC,GAAG,CAACiC,kCAAkC,GAC/D,IAAIC,IAAIzD,OAAOK,OAAO,CAAC2B,GAAG,IAC1BC;IAEJ,MAAM5B,UAAU,IAAIP,gBAAgB;QAClCM,MAAMJ,OAAOI,IAAI;QACjB,mDAAmD;QACnDH,OAAOb,0BAA0BmE,cAAc,MAAMH,QAAQ;QAC7DlD,MAAM;YACJwD,MAAM1D,OAAOK,OAAO,CAACqD,IAAI;YACzBC,KAAK3D,OAAOK,OAAO,CAACsD,GAAG;YACvBjD,SAASsC;YACTY,IAAI5D,OAAOK,OAAO,CAACuD,EAAE;YACrBC,QAAQ7D,OAAOK,OAAO,CAACwD,MAAM;YAC7B3B,YAAYlC,OAAOK,OAAO,CAAC6B,UAAU;YACrC4B,QAAQ9D,OAAOK,OAAO,CAACyD,MAAM;QAC/B;IACF;IAEA;;;;GAIC,GACD,IAAIhB,mBAAmB;QACrBiB,OAAOC,cAAc,CAAC3D,SAAS,YAAY;YACzC4D,YAAY;YACZ7B,OAAO;QACT;IACF;IAEA,IACE,CAAC,AAAC8B,WAAmBC,kBAAkB,IACvC,AAACnE,OAAeoE,gBAAgB,EAChC;QACEF,WAAmBC,kBAAkB,GAAG,IAAI,AAC5CnE,OACAoE,gBAAgB,CAAC;YACjBC,QAAQ;YACRC,YAAY;YACZC,aAAajD,QAAQC,GAAG,CAACiD,QAAQ,KAAK;YACtCC,qBAAqBnD,QAAQC,GAAG,CAACmD,6BAA6B;YAC9DC,KAAKrD,QAAQC,GAAG,CAACiD,QAAQ,KAAK;YAC9BxB,gBAAgBhD,OAAOK,OAAO,CAACK,OAAO;YACtCkE,iBAAiB;YACjBC,sBAAsB;gBACpB,OAAO;oBACLC,SAAS,CAAC;oBACVC,QAAQ,CAAC;oBACTC,eAAe,CAAC;oBAChBC,gBAAgB,EAAE;oBAClBC,SAASrF;gBACX;YACF;QACF;IACF;IAEA,MAAMsF,QAAQ,IAAIrG,eAAe;QAAEuB;QAASD,MAAMJ,OAAOI,IAAI;IAAC;IAC9D,IAAIgF;IACJ,IAAIC;IAEJD,WAAW,MAAMpE,WAAWX,SAAS;QACnC,8DAA8D;QAC9D,MAAMiF,eACJtF,OAAOI,IAAI,KAAK,iBAAiBJ,OAAOI,IAAI,KAAK;QACnD,IAAIkF,cAAc;YAChB,OAAO3F,YAAY4F,KAAK,CACtB3F,eAAe4F,OAAO,EACtB;gBACEC,UAAU,CAAC,WAAW,EAAEpF,QAAQwD,MAAM,CAAC,CAAC,EAAExD,QAAQqF,OAAO,CAAC3C,QAAQ,CAAC,CAAC;gBACpE4C,YAAY;oBACV,eAAetF,QAAQqF,OAAO,CAAC3C,QAAQ;oBACvC,eAAe1C,QAAQwD,MAAM;gBAC/B;YACF,GACA,IACEpE,2BAA2BmG,IAAI,CAC7BlG,qBACA;oBACEmG,KAAKxF;oBACLyF,YAAY;wBACVC,iBAAiB,CAACC;4BAChBX,sBAAsBW;wBACxB;wBACA,2EAA2E;wBAC3EC,cAAcpG;oBAChB;gBACF,GACA,IAAMG,OAAOkG,OAAO,CAAC7F,SAAS8E;QAGtC;QACA,OAAOnF,OAAOkG,OAAO,CAAC7F,SAAS8E;IACjC;IAEA,yCAAyC;IACzC,IAAIC,YAAY,CAAEA,CAAAA,oBAAoBe,QAAO,GAAI;QAC/C,MAAM,IAAIC,UAAU;IACtB;IAEA,IAAIhB,YAAYC,qBAAqB;QACnCD,SAAS1E,OAAO,CAAC4C,GAAG,CAAC,cAAc+B;IACrC;IAEA;;;;;GAKC,GACD,MAAMgB,UAAUjB,4BAAAA,SAAU1E,OAAO,CAACG,GAAG,CAAC;IACtC,IAAIuE,YAAYiB,WAAW,CAACxE,iBAAiB;QAC3C,MAAMyE,aAAa,IAAInH,QAAQkH,SAAS;YACtCE,aAAa;YACb7F,SAASV,OAAOK,OAAO,CAACK,OAAO;YAC/BwB,YAAYlC,OAAOK,OAAO,CAAC6B,UAAU;QACvC;QAEA,IAAI,CAACZ,QAAQC,GAAG,CAACiC,kCAAkC,EAAE;YACnD,IAAI8C,WAAWE,IAAI,KAAKnG,QAAQqF,OAAO,CAACc,IAAI,EAAE;gBAC5CF,WAAWzD,OAAO,GAAGA,WAAWyD,WAAWzD,OAAO;gBAClDuC,SAAS1E,OAAO,CAAC4C,GAAG,CAAC,wBAAwBmD,OAAOH;YACtD;QACF;QAEA;;;;KAIC,GACD,MAAMI,qBAAqBzH,cACzBwH,OAAOH,aACPG,OAAOxE;QAGT,IACEa,qBACA,kDAAkD;QAClD,oDAAoD;QACpD,yCAAyC;QACzC,CACExB,CAAAA,QAAQC,GAAG,CAACoF,0CAA0C,IACtDD,mBAAmBE,KAAK,CAAC,gBAAe,GAE1C;YACAxB,SAAS1E,OAAO,CAAC4C,GAAG,CAAC,oBAAoBoD;QAC3C;IACF;IAEA;;;;GAIC,GACD,MAAMG,WAAWzB,4BAAAA,SAAU1E,OAAO,CAACG,GAAG,CAAC;IACvC,IAAIuE,YAAYyB,YAAY,CAAChF,iBAAiB;QAC5C,MAAMiF,cAAc,IAAI3H,QAAQ0H,UAAU;YACxCN,aAAa;YACb7F,SAASV,OAAOK,OAAO,CAACK,OAAO;YAC/BwB,YAAYlC,OAAOK,OAAO,CAAC6B,UAAU;QACvC;QAEA;;;KAGC,GACDkD,WAAW,IAAIe,SAASf,SAAS1B,IAAI,EAAE0B;QAEvC,IAAI,CAAC9D,QAAQC,GAAG,CAACiC,kCAAkC,EAAE;YACnD,IAAIsD,YAAYN,IAAI,KAAKnG,QAAQqF,OAAO,CAACc,IAAI,EAAE;gBAC7CM,YAAYjE,OAAO,GAAGA,WAAWiE,YAAYjE,OAAO;gBACpDuC,SAAS1E,OAAO,CAAC4C,GAAG,CAAC,YAAYmD,OAAOK;YAC1C;QACF;QAEA;;;;KAIC,GACD,IAAIhE,mBAAmB;YACrBsC,SAAS1E,OAAO,CAACgC,MAAM,CAAC;YACxB0C,SAAS1E,OAAO,CAAC4C,GAAG,CAClB,qBACArE,cAAcwH,OAAOK,cAAcL,OAAOxE;QAE9C;IACF;IAEA,MAAM8E,gBAAgB3B,WAAWA,WAAWpG,aAAagI,IAAI;IAE7D,iFAAiF;IACjF,MAAMC,4BAA4BF,cAAcrG,OAAO,CAACG,GAAG,CACzD;IAEF,MAAMqG,qBAA+B,EAAE;IACvC,IAAID,2BAA2B;QAC7B,KAAK,MAAM,CAACnG,KAAKsB,MAAM,IAAIa,cAAe;YACxC8D,cAAcrG,OAAO,CAAC4C,GAAG,CAAC,CAAC,qBAAqB,EAAExC,IAAI,CAAC,EAAEsB;YACzD8E,mBAAmBC,IAAI,CAACrG;QAC1B;QAEA,IAAIoG,mBAAmBzE,MAAM,GAAG,GAAG;YACjCsE,cAAcrG,OAAO,CAAC4C,GAAG,CACvB,iCACA2D,4BAA4B,MAAMC,mBAAmBE,IAAI,CAAC;QAE9D;IACF;IAEA,OAAO;QACLhC,UAAU2B;QACVxG,WAAW8G,QAAQC,GAAG,CAACnC,KAAK,CAACjG,gBAAgB;QAC7CqI,cAAclH,QAAQkH,YAAY;IACpC;AACF"}