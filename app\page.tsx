"use client"

import { useState } from "react"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Check, Scissors, ArrowRight, Star, Zap, Shield, Clock } from "lucide-react"
import { motion } from "framer-motion"
import { cn } from "@/lib/utils"
import { TestimonialCard } from "@/components/testimonial-card"

export default function LandingPage() {
  const [selectedPlan, setSelectedPlan] = useState<"free" | "pro" | "clipper">("pro")

  const plans = [
    {
      name: "Free",
      price: "$0",
      description: "For casual creators",
      features: ["Link input", "Basic crop", "Default subtitles", "3 clips/day"],
      cta: "Get Started",
      popular: false,
    },
    {
      name: "Pro",
      price: "$19",
      period: "/month",
      description: "For serious creators",
      features: [
        "Everything in Free",
        "Subtitle styling",
        "Hook detection",
        "Speed boost",
        "Social captions",
        "50 clips/month",
      ],
      cta: "Start Free Trial",
      popular: true,
    },
    {
      name: "Clipper",
      price: "$49",
      period: "/month",
      description: "For power creators",
      features: [
        "Everything in Pro",
        "Clip storage",
        "Batch upload",
        "Trending sounds",
        "Team sharing",
        "Unlimited clips",
      ],
      cta: "Start Free Trial",
      popular: false,
    },
  ]

  const testimonials = [
    {
      quote:
        "ClipSnip saved me hours of editing time. I can now repurpose my long YouTube videos into TikToks in seconds.",
      author: "Alex Johnson",
      role: "Tech YouTuber, 500K subscribers",
      rating: 5,
    },
    {
      quote:
        "The automatic subtitles are perfect. My engagement has increased by 35% since I started using ClipSnip for my content.",
      author: "Sarah Miller",
      role: "Fitness Influencer, 1.2M followers",
      rating: 5,
    },
    {
      quote:
        "As a small business, we needed to create social content quickly. ClipSnip has been a game-changer for our marketing team.",
      author: "Michael Chen",
      role: "Marketing Director, Bloom Cosmetics",
      rating: 5,
    },
  ]

  const features = [
    {
      icon: <Zap className="h-8 w-8" />,
      title: "Lightning Fast Processing",
      description: "Transform your videos into viral clips in under 60 seconds with our advanced AI technology.",
    },
    {
      icon: <Shield className="h-8 w-8" />,
      title: "Premium Quality Output",
      description: "Export in 4K resolution with professional-grade subtitles and perfect aspect ratios.",
    },
    {
      icon: <Clock className="h-8 w-8" />,
      title: "Save Hours of Work",
      description: "What used to take hours of manual editing now happens automatically in minutes.",
    },
  ]

  return (
    <div className="flex min-h-screen flex-col" style={{backgroundColor: '#f8f8f6'}}>
      {/* Header */}
      <header className="w-full bg-luxury-green text-white py-4 px-6 shadow-lg">
        <div className="max-w-6xl mx-auto flex justify-between items-center">
          <div className="flex items-center gap-2">
            <Scissors className="h-6 w-6 text-luxury-gold" />
            <span className="font-bold text-xl">clipsnip</span>
          </div>

          <div className="flex items-center gap-4">
            <nav className="hidden md:flex items-center gap-6">
              <a href="#features" className="text-white/90 hover:text-luxury-gold transition-colors">
                Features
              </a>
              <a href="#testimonials" className="text-white/90 hover:text-luxury-gold transition-colors">
                Testimonials
              </a>
              <a href="#plans" className="text-white/90 hover:text-luxury-gold transition-colors">
                Plans
              </a>
            </nav>

            <Link href="/login">
              <Button
                variant="secondary"
                size="sm"
                className="bg-luxury-gold text-luxury-green hover:bg-luxury-gold-light font-medium"
              >
                Login
              </Button>
            </Link>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="w-full py-20 md:py-32 px-4">
        <div className="max-w-6xl mx-auto">
          <div className="flex flex-col lg:flex-row items-center gap-12">
            <div className="w-full lg:w-1/2 text-center lg:text-left">
              <motion.div
                className="inline-flex items-center gap-2 bg-luxury-green/10 text-luxury-green px-4 py-2 rounded-full mb-6"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
              >
                <Star className="h-4 w-4 text-luxury-gold" />
                <span className="text-sm font-medium">Trusted by 50,000+ creators</span>
              </motion.div>

              <motion.h1
                className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 leading-tight"
                style={{color: '#121212'}}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.1 }}
              >
                Create Viral Clips with
                <span className="text-luxury-green"> Royal Precision</span>
              </motion.h1>
              <motion.p
                className="text-xl mb-8 leading-relaxed"
                style={{color: '#121212'}}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.2 }}
              >
                Transform any video into a short-form masterpiece with automatic cropping, premium subtitles, and
                AI-powered editing.
              </motion.p>

              <motion.div
                className="flex flex-col sm:flex-row gap-4 items-center justify-center lg:justify-start"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: 0.3 }}
              >
                <Link href="/signup">
                  <Button className="h-14 px-8 text-lg font-medium rounded-2xl bg-luxury-green hover:shadow-xl hover:scale-105 transition-all duration-300">
                    Start Creating Now
                  </Button>
                </Link>
                <Link href="#features">
                  <Button
                    variant="outline"
                    className="h-14 px-8 text-lg font-medium rounded-2xl border-2 border-luxury-green text-luxury-green hover:bg-luxury-green hover:text-white transition-all duration-300"
                  >
                    See How It Works
                  </Button>
                </Link>
              </motion.div>
              <p className="text-sm mt-4" style={{color: '#121212'}}>No credit card required. 3 free clips to start.</p>
            </div>

            <div className="w-full lg:w-1/2">
              <div className="relative">
                <div className="absolute -inset-4 bg-luxury-gold/20 rounded-3xl blur-xl opacity-70"></div>
                <div className="relative bg-white rounded-2xl shadow-2xl overflow-hidden border border-luxury-gold/20">
                  <div className="aspect-[4/3] flex items-center justify-center" style={{backgroundColor: '#f8f8f6'}}>
                    <div className="relative w-[280px] h-[500px] rounded-3xl overflow-hidden shadow-2xl border-8" style={{backgroundColor: '#121212', borderColor: '#121212'}}>
                      <div className="absolute top-0 left-0 right-0 h-6 rounded-t-xl" style={{backgroundColor: '#121212'}}></div>
                      <div className="h-full w-full bg-luxury-green/20 flex items-center justify-center">
                        <div className="text-center text-white">
                          <Scissors className="h-12 w-12 mx-auto mb-4 text-luxury-gold" />
                          <p className="text-lg font-medium">Your viral clip</p>
                          <p className="text-sm opacity-70">Ready to dominate</p>
                        </div>
                      </div>
                      <div className="absolute bottom-6 left-0 right-0 px-4">
                        <div className="backdrop-blur-sm text-white p-3 rounded-xl border border-luxury-gold/30" style={{backgroundColor: '#121212'}}>
                          <p className="text-sm font-medium">Premium subtitles</p>
                          <p className="text-xs opacity-80">Crafted for engagement</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="w-full py-20 px-4" style={{backgroundColor: '#ffffff'}}>
        <div className="max-w-6xl mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4 text-luxury-green">Why Creators Choose ClipSnip</h2>
          <p className="text-xl mb-16 max-w-3xl mx-auto" style={{color: '#121212'}}>
            Experience the luxury of effortless content creation with our premium AI-powered tools.
          </p>

          <div className="grid md:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <motion.div
                key={index}
                className="bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-luxury-gold/10"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, delay: index * 0.1 }}
              >
                <div className="w-16 h-16 bg-luxury-green rounded-2xl flex items-center justify-center mx-auto mb-6 text-white">
                  {feature.icon}
                </div>
                <h3 className="text-xl font-bold mb-4 text-luxury-green">{feature.title}</h3>
                <p className="leading-relaxed" style={{color: '#121212'}}>{feature.description}</p>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section id="testimonials" className="w-full py-20 px-4 bg-white">
        <div className="max-w-6xl mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4 text-luxury-green">Trusted by Elite Creators</h2>
          <p className="text-xl mb-16" style={{color: '#121212'}}>
            Join thousands of successful content creators who've elevated their game
          </p>

          <div className="grid md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <TestimonialCard key={index} testimonial={testimonial} />
            ))}
          </div>
        </div>
      </section>

      {/* Plans Section */}
      <section id="plans" className="w-full py-20 px-4" style={{backgroundColor: '#ffffff'}}>
        <div className="max-w-6xl mx-auto text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4 text-luxury-green">Choose Your Plan</h2>
          <p className="text-xl mb-16 max-w-3xl mx-auto" style={{color: '#121212'}}>
            Select the perfect plan for your content creation empire.
          </p>

          <div className="grid md:grid-cols-3 gap-8">
            {plans.map((plan) => (
              <Card
                key={plan.name}
                className={cn(
                  "rounded-2xl shadow-lg overflow-hidden border-2 transition-all hover:shadow-xl hover:scale-105 duration-300",
                  selectedPlan === plan.name.toLowerCase()
                    ? "border-luxury-gold shadow-luxury-gold/20"
                    : "border-transparent",
                  plan.popular && "ring-2 ring-luxury-gold ring-opacity-50",
                )}
                onClick={() => setSelectedPlan(plan.name.toLowerCase() as "free" | "pro" | "clipper")}
              >
                {plan.popular && (
                  <div className="absolute top-0 right-0 bg-luxury-gold text-luxury-green px-4 py-1 text-sm font-bold rounded-bl-lg">
                    Most Popular
                  </div>
                )}
                <CardHeader
                  className={cn(
                    "pb-4",
                    plan.name === "Clipper" ? "bg-luxury-green/10" : "",
                  )}
                >
                  <CardTitle className="text-2xl text-luxury-green">
                    {plan.name}
                    {plan.name === "Clipper" && (
                      <span className="ml-2 text-xs bg-luxury-gold text-luxury-green px-2 py-0.5 rounded-full font-bold">
                        Premium
                      </span>
                    )}
                  </CardTitle>
                  <div className="flex items-baseline mt-2">
                    <span className="text-4xl font-bold text-luxury-green">{plan.price}</span>
                    {plan.period && <span style={{color: '#121212'}}>{plan.period}</span>}
                  </div>
                  <CardDescription style={{color: '#121212'}}>{plan.description}</CardDescription>
                </CardHeader>
                <CardContent className="pt-4">
                  <ul className="space-y-3">
                    {plan.features.map((feature) => (
                      <li key={feature} className="flex items-start gap-3">
                        <Check className="h-5 w-5 text-luxury-gold shrink-0 mt-0.5" />
                        <span style={{color: '#121212'}}>{feature}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
                <CardFooter>
                  <Link href="/signup" className="w-full">
                    <Button
                      className={cn(
                        "w-full rounded-xl font-medium transition-all duration-300",
                        plan.name === "Free"
                          ? "hover:shadow-lg hover:scale-105"
                          : "bg-luxury-green hover:shadow-lg hover:scale-105",
                      )}
                      style={plan.name === "Free" ? {backgroundColor: '#f8f8f6', color: '#121212'} : {}}
                    >
                      {plan.cta}
                    </Button>
                  </Link>
                </CardFooter>
              </Card>
            ))}
          </div>

          <div className="mt-16">
            <Link href="/signup">
              <Button className="h-14 px-8 text-lg font-medium rounded-2xl bg-luxury-green hover:shadow-xl hover:scale-105 transition-all duration-300">
                Start Your Journey <ArrowRight className="ml-2 h-5 w-5" />
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="w-full py-12 px-6 bg-luxury-green text-white">
        <div className="max-w-6xl mx-auto">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8 mb-8">
            <div>
              <div className="flex items-center gap-2 mb-4">
                <Scissors className="h-6 w-6 text-luxury-gold" />
                <span className="font-bold text-xl">clipsnip</span>
              </div>
              <p className="text-white/80 text-sm leading-relaxed">
                Premium AI-powered video editing. Create viral clips with royal precision.
              </p>
            </div>

            <div>
              <h4 className="font-bold mb-4 text-luxury-gold">Product</h4>
              <ul className="space-y-2">
                <li>
                  <a href="#features" className="text-white/80 hover:text-luxury-gold transition-colors text-sm">
                    Features
                  </a>
                </li>
                <li>
                  <a href="#plans" className="text-white/80 hover:text-luxury-gold transition-colors text-sm">
                    Plans
                  </a>
                </li>
                <li>
                  <a href="#testimonials" className="text-white/80 hover:text-luxury-gold transition-colors text-sm">
                    Testimonials
                  </a>
                </li>
              </ul>
            </div>

            <div>
              <h4 className="font-bold mb-4 text-luxury-gold">Company</h4>
              <ul className="space-y-2">
                <li>
                  <a href="#" className="text-white/80 hover:text-luxury-gold transition-colors text-sm">
                    About
                  </a>
                </li>
                <li>
                  <a href="#" className="text-white/80 hover:text-luxury-gold transition-colors text-sm">
                    Blog
                  </a>
                </li>
                <li>
                  <a href="#" className="text-white/80 hover:text-luxury-gold transition-colors text-sm">
                    Careers
                  </a>
                </li>
              </ul>
            </div>

            <div>
              <h4 className="font-bold mb-4 text-luxury-gold">Support</h4>
              <ul className="space-y-2">
                <li>
                  <a href="#" className="text-white/80 hover:text-luxury-gold transition-colors text-sm">
                    Contact
                  </a>
                </li>
                <li>
                  <a href="#" className="text-white/80 hover:text-luxury-gold transition-colors text-sm">
                    Terms
                  </a>
                </li>
                <li>
                  <a href="#" className="text-white/80 hover:text-luxury-gold transition-colors text-sm">
                    Privacy
                  </a>
                </li>
              </ul>
            </div>
          </div>

          <div className="pt-8 border-t border-white/20 flex flex-col md:flex-row justify-between items-center">
            <p className="text-white/60 text-sm mb-4 md:mb-0">
              © {new Date().getFullYear()} ClipSnip. All rights reserved.
            </p>
            <div className="flex items-center gap-2 text-sm text-white/60">
              <span>Crafted with</span>
              <span className="text-luxury-gold">♔</span>
              <span>for creators</span>
            </div>
          </div>
        </div>
      </footer>
    </div>
  )
}
