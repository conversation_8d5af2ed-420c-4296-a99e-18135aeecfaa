{"name": "clipsnip", "version": "1.0.0", "description": "Simple video clipping and cropping site", "main": "server.js", "scripts": {"dev": "concurrently \"npm run server\" \"npm run client\"", "server": "nodemon server.js", "client": "cd client && npm start", "build": "cd client && npm run build", "start": "node server.js"}, "dependencies": {"express": "^4.18.2", "multer": "^1.4.5-lts.1", "cors": "^2.8.5", "dotenv": "^16.3.1", "fluent-ffmpeg": "^2.1.2", "uuid": "^9.0.1"}, "devDependencies": {"nodemon": "^3.0.1", "concurrently": "^8.2.2"}, "keywords": ["video", "clipping", "cropping", "ffmpeg"], "author": "", "license": "MIT"}