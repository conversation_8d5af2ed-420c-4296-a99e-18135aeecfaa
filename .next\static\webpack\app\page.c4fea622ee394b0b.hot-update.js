"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ LandingPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Check_Clock_Scissors_Shield_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Check,Clock,Scissors,Shield,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Check_Clock_Scissors_Shield_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Check,Clock,Scissors,Shield,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Check_Clock_Scissors_Shield_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Check,Clock,Scissors,Shield,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Check_Clock_Scissors_Shield_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Check,Clock,Scissors,Shield,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/scissors.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Check_Clock_Scissors_Shield_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Check,Clock,Scissors,Shield,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Check_Clock_Scissors_Shield_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Check,Clock,Scissors,Shield,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Check_Clock_Scissors_Shield_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Check,Clock,Scissors,Shield,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./lib/utils.ts\");\n/* harmony import */ var _components_testimonial_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/testimonial-card */ \"(app-pages-browser)/./components/testimonial-card.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction LandingPage() {\n    _s();\n    const [selectedPlan, setSelectedPlan] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"pro\");\n    const plans = [\n        {\n            name: \"Free\",\n            price: \"$0\",\n            description: \"For casual creators\",\n            features: [\n                \"Link input\",\n                \"Basic crop\",\n                \"Default subtitles\",\n                \"3 clips/day\"\n            ],\n            cta: \"Get Started\",\n            popular: false\n        },\n        {\n            name: \"Pro\",\n            price: \"$19\",\n            period: \"/month\",\n            description: \"For serious creators\",\n            features: [\n                \"Everything in Free\",\n                \"Subtitle styling\",\n                \"Hook detection\",\n                \"Speed boost\",\n                \"Social captions\",\n                \"50 clips/month\"\n            ],\n            cta: \"Start Free Trial\",\n            popular: true\n        },\n        {\n            name: \"Clipper\",\n            price: \"$49\",\n            period: \"/month\",\n            description: \"For power creators\",\n            features: [\n                \"Everything in Pro\",\n                \"Clip storage\",\n                \"Batch upload\",\n                \"Trending sounds\",\n                \"Team sharing\",\n                \"Unlimited clips\"\n            ],\n            cta: \"Start Free Trial\",\n            popular: false\n        }\n    ];\n    const testimonials = [\n        {\n            quote: \"ClipSnip saved me hours of editing time. I can now repurpose my long YouTube videos into TikToks in seconds.\",\n            author: \"Alex Johnson\",\n            role: \"Tech YouTuber, 500K subscribers\",\n            rating: 5\n        },\n        {\n            quote: \"The automatic subtitles are perfect. My engagement has increased by 35% since I started using ClipSnip for my content.\",\n            author: \"Sarah Miller\",\n            role: \"Fitness Influencer, 1.2M followers\",\n            rating: 5\n        },\n        {\n            quote: \"As a small business, we needed to create social content quickly. ClipSnip has been a game-changer for our marketing team.\",\n            author: \"Michael Chen\",\n            role: \"Marketing Director, Bloom Cosmetics\",\n            rating: 5\n        }\n    ];\n    const features = [\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Check_Clock_Scissors_Shield_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                className: \"h-8 w-8\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                lineNumber: 84,\n                columnNumber: 13\n            }, this),\n            title: \"Lightning Fast Processing\",\n            description: \"Transform your videos into viral clips in under 60 seconds with our advanced AI technology.\"\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Check_Clock_Scissors_Shield_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                className: \"h-8 w-8\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                lineNumber: 89,\n                columnNumber: 13\n            }, this),\n            title: \"Premium Quality Output\",\n            description: \"Export in 4K resolution with professional-grade subtitles and perfect aspect ratios.\"\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Check_Clock_Scissors_Shield_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                className: \"h-8 w-8\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                lineNumber: 94,\n                columnNumber: 13\n            }, this),\n            title: \"Save Hours of Work\",\n            description: \"What used to take hours of manual editing now happens automatically in minutes.\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex min-h-screen flex-col\",\n        style: {\n            backgroundColor: \"#f8f8f6\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"w-full bg-luxury-green text-white py-4 px-6 shadow-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-6xl mx-auto flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Check_Clock_Scissors_Shield_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-6 w-6 text-luxury-gold\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-bold text-xl\",\n                                    children: \"clipsnip\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"hidden md:flex items-center gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#features\",\n                                            className: \"text-white/90 hover:text-luxury-gold transition-colors\",\n                                            children: \"Features\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#testimonials\",\n                                            className: \"text-white/90 hover:text-luxury-gold transition-colors\",\n                                            children: \"Testimonials\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#plans\",\n                                            className: \"text-white/90 hover:text-luxury-gold transition-colors\",\n                                            children: \"Plans\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                            lineNumber: 118,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: \"/login\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"secondary\",\n                                        size: \"sm\",\n                                        className: \"bg-luxury-gold text-luxury-green hover:bg-luxury-gold-light font-medium\",\n                                        children: \"Login\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                    lineNumber: 104,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                lineNumber: 103,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"w-full py-20 md:py-32 px-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-6xl mx-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col lg:flex-row items-center gap-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full lg:w-1/2 text-center lg:text-left\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                        className: \"inline-flex items-center gap-2 bg-luxury-green/10 text-luxury-green px-4 py-2 rounded-full mb-6\",\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            duration: 0.5\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Check_Clock_Scissors_Shield_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"h-4 w-4 text-luxury-gold\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                lineNumber: 147,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium\",\n                                                children: \"Trusted by 50,000+ creators\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                lineNumber: 148,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.h1, {\n                                        className: \"text-4xl md:text-5xl lg:text-6xl font-bold mb-6 leading-tight\",\n                                        style: {\n                                            color: \"#121212\"\n                                        },\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            duration: 0.5,\n                                            delay: 0.1\n                                        },\n                                        children: [\n                                            \"Create Viral Clips with\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-luxury-green\",\n                                                children: \" Royal Precision\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                lineNumber: 159,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.p, {\n                                        className: \"text-xl mb-8 leading-relaxed\",\n                                        style: {\n                                            color: \"#121212\"\n                                        },\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            duration: 0.5,\n                                            delay: 0.2\n                                        },\n                                        children: \"Transform any video into a short-form masterpiece with automatic cropping, premium subtitles, and AI-powered editing.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                        className: \"flex flex-col sm:flex-row gap-4 items-center justify-center lg:justify-start\",\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            duration: 0.5,\n                                            delay: 0.3\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                href: \"/signup\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    className: \"h-14 px-8 text-lg font-medium rounded-2xl bg-luxury-green hover:shadow-xl hover:scale-105 transition-all duration-300\",\n                                                    children: \"Start Creating Now\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                    lineNumber: 179,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                href: \"#features\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: \"outline\",\n                                                    className: \"h-14 px-8 text-lg font-medium rounded-2xl border-2 border-luxury-green text-luxury-green hover:bg-luxury-green hover:text-white transition-all duration-300\",\n                                                    children: \"See How It Works\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                    lineNumber: 184,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                lineNumber: 183,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm mt-4\",\n                                        style: {\n                                            color: \"#121212\"\n                                        },\n                                        children: \"No credit card required. 3 free clips to start.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                        lineNumber: 192,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full lg:w-1/2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute -inset-4 bg-gradient-to-r from-luxury-gold/20 to-luxury-green/20 rounded-3xl blur-xl opacity-70\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative bg-white rounded-2xl shadow-2xl overflow-hidden border border-luxury-gold/20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"aspect-[4/3] bg-gradient-to-br from-gray-50 to-gray-100 flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative w-[280px] h-[500px] bg-black rounded-3xl overflow-hidden shadow-2xl border-8 border-black\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute top-0 left-0 right-0 h-6 bg-black rounded-t-xl\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                            lineNumber: 201,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-full w-full bg-gradient-to-br from-luxury-green/20 to-luxury-gold/20 flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-center text-white\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Check_Clock_Scissors_Shield_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                        className: \"h-12 w-12 mx-auto mb-4 text-luxury-gold\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 204,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-lg font-medium\",\n                                                                        children: \"Your viral clip\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 205,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm opacity-70\",\n                                                                        children: \"Ready to dominate\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 206,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                                lineNumber: 203,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                            lineNumber: 202,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute bottom-6 left-0 right-0 px-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"bg-black/50 backdrop-blur-sm text-white p-3 rounded-xl border border-luxury-gold/30\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm font-medium\",\n                                                                        children: \"Premium subtitles\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 211,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs opacity-80\",\n                                                                        children: \"Crafted for engagement\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 212,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                                lineNumber: 210,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                            lineNumber: 209,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                    lineNumber: 200,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                    lineNumber: 138,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                lineNumber: 137,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"features\",\n                className: \"w-full py-20 px-4 bg-gradient-to-br from-luxury-green/5 to-luxury-gold/5\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-6xl mx-auto text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl md:text-4xl font-bold mb-4 text-luxury-green\",\n                            children: \"Why Creators Choose ClipSnip\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                            lineNumber: 227,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-600 mb-16 max-w-3xl mx-auto\",\n                            children: \"Experience the luxury of effortless content creation with our premium AI-powered tools.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                            lineNumber: 228,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-3 gap-8\",\n                            children: features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                    className: \"bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-luxury-gold/10\",\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.5,\n                                        delay: index * 0.1\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-gradient-to-br from-luxury-green to-luxury-gold rounded-2xl flex items-center justify-center mx-auto mb-6 text-white\",\n                                            children: feature.icon\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                            lineNumber: 241,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-bold mb-4 text-luxury-green\",\n                                            children: feature.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                            lineNumber: 244,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 leading-relaxed\",\n                                            children: feature.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                            lineNumber: 245,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                            lineNumber: 232,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                    lineNumber: 226,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                lineNumber: 225,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"testimonials\",\n                className: \"w-full py-20 px-4 bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-6xl mx-auto text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl md:text-4xl font-bold mb-4 text-luxury-green\",\n                            children: \"Trusted by Elite Creators\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-600 mb-16\",\n                            children: \"Join thousands of successful content creators who've elevated their game\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                            lineNumber: 256,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-3 gap-8\",\n                            children: testimonials.map((testimonial, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_testimonial_card__WEBPACK_IMPORTED_MODULE_6__.TestimonialCard, {\n                                    testimonial: testimonial\n                                }, index, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                            lineNumber: 260,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                    lineNumber: 254,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                lineNumber: 253,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"plans\",\n                className: \"w-full py-20 px-4 bg-gradient-to-br from-luxury-green/5 to-luxury-gold/5\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-6xl mx-auto text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl md:text-4xl font-bold mb-4 text-luxury-green\",\n                            children: \"Choose Your Plan\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                            lineNumber: 271,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-600 mb-16 max-w-3xl mx-auto\",\n                            children: \"Select the perfect plan for your content creation empire.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                            lineNumber: 272,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-3 gap-8\",\n                            children: plans.map((plan)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"rounded-2xl shadow-lg overflow-hidden border-2 transition-all hover:shadow-xl hover:scale-105 duration-300\", selectedPlan === plan.name.toLowerCase() ? \"border-luxury-gold shadow-luxury-gold/20\" : \"border-transparent\", plan.popular && \"ring-2 ring-luxury-gold ring-opacity-50\"),\n                                    onClick: ()=>setSelectedPlan(plan.name.toLowerCase()),\n                                    children: [\n                                        plan.popular && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-0 right-0 bg-gradient-to-r from-luxury-gold to-luxury-gold-light text-luxury-green px-4 py-1 text-sm font-bold rounded-bl-lg\",\n                                            children: \"Most Popular\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                            lineNumber: 290,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"pb-4\", plan.name === \"Clipper\" ? \"bg-gradient-to-r from-luxury-green/10 to-luxury-gold/10\" : \"\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                    className: \"text-2xl text-luxury-green\",\n                                                    children: [\n                                                        plan.name,\n                                                        plan.name === \"Clipper\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"ml-2 text-xs bg-luxury-gold text-luxury-green px-2 py-0.5 rounded-full font-bold\",\n                                                            children: \"Premium\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                            lineNumber: 303,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                    lineNumber: 300,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-baseline mt-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-4xl font-bold text-luxury-green\",\n                                                            children: plan.price\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                            lineNumber: 309,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        plan.period && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-600\",\n                                                            children: plan.period\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                            lineNumber: 310,\n                                                            columnNumber: 37\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                    lineNumber: 308,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                                    className: \"text-gray-600\",\n                                                    children: plan.description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                    lineNumber: 312,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                            lineNumber: 294,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                            className: \"pt-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"space-y-3\",\n                                                children: plan.features.map((feature)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-start gap-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Check_Clock_Scissors_Shield_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-5 w-5 text-luxury-gold shrink-0 mt-0.5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                                lineNumber: 318,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"text-gray-700\",\n                                                                children: feature\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                                lineNumber: 319,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, feature, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                        lineNumber: 317,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                lineNumber: 315,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                            lineNumber: 314,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardFooter, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                href: \"/signup\",\n                                                className: \"w-full\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"w-full rounded-xl font-medium transition-all duration-300\", plan.name === \"Free\" ? \"bg-gray-100 text-gray-900 hover:bg-gray-200\" : \"bg-gradient-to-r from-luxury-green to-luxury-green-dark hover:shadow-lg hover:scale-105\"),\n                                                    children: plan.cta\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                    lineNumber: 326,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                lineNumber: 325,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                            lineNumber: 324,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, plan.name, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                    lineNumber: 278,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                            lineNumber: 276,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-16\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/signup\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    className: \"h-14 px-8 text-lg font-medium rounded-2xl bg-gradient-to-r from-luxury-green to-luxury-green-dark hover:shadow-xl hover:scale-105 transition-all duration-300\",\n                                    children: [\n                                        \"Start Your Journey \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Check_Clock_Scissors_Shield_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"ml-2 h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                            lineNumber: 345,\n                                            columnNumber: 36\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                    lineNumber: 344,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                lineNumber: 343,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                            lineNumber: 342,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                    lineNumber: 270,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                lineNumber: 269,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"w-full py-12 px-6 bg-luxury-green text-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-6xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-4 gap-8 mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Check_Clock_Scissors_Shield_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-6 w-6 text-luxury-gold\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                    lineNumber: 358,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-bold text-xl\",\n                                                    children: \"clipsnip\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                    lineNumber: 359,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                            lineNumber: 357,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white/80 text-sm leading-relaxed\",\n                                            children: \"Premium AI-powered video editing. Create viral clips with royal precision.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                            lineNumber: 361,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                    lineNumber: 356,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-bold mb-4 text-luxury-gold\",\n                                            children: \"Product\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                            lineNumber: 367,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#features\",\n                                                        className: \"text-white/80 hover:text-luxury-gold transition-colors text-sm\",\n                                                        children: \"Features\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                        lineNumber: 370,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                    lineNumber: 369,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#plans\",\n                                                        className: \"text-white/80 hover:text-luxury-gold transition-colors text-sm\",\n                                                        children: \"Plans\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                        lineNumber: 375,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                    lineNumber: 374,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#testimonials\",\n                                                        className: \"text-white/80 hover:text-luxury-gold transition-colors text-sm\",\n                                                        children: \"Testimonials\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                        lineNumber: 380,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                    lineNumber: 379,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                            lineNumber: 368,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                    lineNumber: 366,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-bold mb-4 text-luxury-gold\",\n                                            children: \"Company\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                            lineNumber: 388,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"text-white/80 hover:text-luxury-gold transition-colors text-sm\",\n                                                        children: \"About\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                        lineNumber: 391,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                    lineNumber: 390,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"text-white/80 hover:text-luxury-gold transition-colors text-sm\",\n                                                        children: \"Blog\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                        lineNumber: 396,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                    lineNumber: 395,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"text-white/80 hover:text-luxury-gold transition-colors text-sm\",\n                                                        children: \"Careers\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                        lineNumber: 401,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                    lineNumber: 400,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                            lineNumber: 389,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                    lineNumber: 387,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-bold mb-4 text-luxury-gold\",\n                                            children: \"Support\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                            lineNumber: 409,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"text-white/80 hover:text-luxury-gold transition-colors text-sm\",\n                                                        children: \"Contact\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                        lineNumber: 412,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                    lineNumber: 411,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"text-white/80 hover:text-luxury-gold transition-colors text-sm\",\n                                                        children: \"Terms\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                        lineNumber: 417,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                    lineNumber: 416,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"text-white/80 hover:text-luxury-gold transition-colors text-sm\",\n                                                        children: \"Privacy\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                        lineNumber: 422,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                    lineNumber: 421,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                            lineNumber: 410,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                    lineNumber: 408,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                            lineNumber: 355,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"pt-8 border-t border-white/20 flex flex-col md:flex-row justify-between items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white/60 text-sm mb-4 md:mb-0\",\n                                    children: [\n                                        \"\\xa9 \",\n                                        new Date().getFullYear(),\n                                        \" ClipSnip. All rights reserved.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                    lineNumber: 431,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 text-sm text-white/60\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Crafted with\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                            lineNumber: 435,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-luxury-gold\",\n                                            children: \"♔\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                            lineNumber: 436,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"for creators\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                            lineNumber: 437,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                    lineNumber: 434,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                            lineNumber: 430,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                    lineNumber: 354,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                lineNumber: 353,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n        lineNumber: 101,\n        columnNumber: 5\n    }, this);\n}\n_s(LandingPage, \"DzoslR4k2TpIBywZQz/PcUL7kh4=\");\n_c = LandingPage;\nvar _c;\n$RefreshReg$(_c, \"LandingPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});