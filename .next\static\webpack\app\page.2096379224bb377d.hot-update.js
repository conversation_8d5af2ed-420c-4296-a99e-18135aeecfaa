"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ LandingPage; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Check_Clock_Scissors_Shield_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Check,Clock,Scissors,Shield,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/zap.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Check_Clock_Scissors_Shield_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Check,Clock,Scissors,Shield,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Check_Clock_Scissors_Shield_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Check,Clock,Scissors,Shield,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Check_Clock_Scissors_Shield_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Check,Clock,Scissors,Shield,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/scissors.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Check_Clock_Scissors_Shield_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Check,Clock,Scissors,Shield,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Check_Clock_Scissors_Shield_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Check,Clock,Scissors,Shield,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Check_Clock_Scissors_Shield_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Check,Clock,Scissors,Shield,Star,Zap!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/utils */ \"(app-pages-browser)/./lib/utils.ts\");\n/* harmony import */ var _components_testimonial_card__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/testimonial-card */ \"(app-pages-browser)/./components/testimonial-card.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction LandingPage() {\n    _s();\n    const [selectedPlan, setSelectedPlan] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"pro\");\n    const plans = [\n        {\n            name: \"Free\",\n            price: \"$0\",\n            description: \"For casual creators\",\n            features: [\n                \"Link input\",\n                \"Basic crop\",\n                \"Default subtitles\",\n                \"3 clips/day\"\n            ],\n            cta: \"Get Started\",\n            popular: false\n        },\n        {\n            name: \"Pro\",\n            price: \"$19\",\n            period: \"/month\",\n            description: \"For serious creators\",\n            features: [\n                \"Everything in Free\",\n                \"Subtitle styling\",\n                \"Hook detection\",\n                \"Speed boost\",\n                \"Social captions\",\n                \"50 clips/month\"\n            ],\n            cta: \"Start Free Trial\",\n            popular: true\n        },\n        {\n            name: \"Clipper\",\n            price: \"$49\",\n            period: \"/month\",\n            description: \"For power creators\",\n            features: [\n                \"Everything in Pro\",\n                \"Clip storage\",\n                \"Batch upload\",\n                \"Trending sounds\",\n                \"Team sharing\",\n                \"Unlimited clips\"\n            ],\n            cta: \"Start Free Trial\",\n            popular: false\n        }\n    ];\n    const testimonials = [\n        {\n            quote: \"ClipSnip saved me hours of editing time. I can now repurpose my long YouTube videos into TikToks in seconds.\",\n            author: \"Alex Johnson\",\n            role: \"Tech YouTuber, 500K subscribers\",\n            rating: 5\n        },\n        {\n            quote: \"The automatic subtitles are perfect. My engagement has increased by 35% since I started using ClipSnip for my content.\",\n            author: \"Sarah Miller\",\n            role: \"Fitness Influencer, 1.2M followers\",\n            rating: 5\n        },\n        {\n            quote: \"As a small business, we needed to create social content quickly. ClipSnip has been a game-changer for our marketing team.\",\n            author: \"Michael Chen\",\n            role: \"Marketing Director, Bloom Cosmetics\",\n            rating: 5\n        }\n    ];\n    const features = [\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Check_Clock_Scissors_Shield_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                className: \"h-8 w-8\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                lineNumber: 84,\n                columnNumber: 13\n            }, this),\n            title: \"Lightning Fast Processing\",\n            description: \"Transform your videos into viral clips in under 60 seconds with our advanced AI technology.\"\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Check_Clock_Scissors_Shield_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                className: \"h-8 w-8\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                lineNumber: 89,\n                columnNumber: 13\n            }, this),\n            title: \"Premium Quality Output\",\n            description: \"Export in 4K resolution with professional-grade subtitles and perfect aspect ratios.\"\n        },\n        {\n            icon: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Check_Clock_Scissors_Shield_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                className: \"h-8 w-8\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                lineNumber: 94,\n                columnNumber: 13\n            }, this),\n            title: \"Save Hours of Work\",\n            description: \"What used to take hours of manual editing now happens automatically in minutes.\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex min-h-screen flex-col\",\n        style: {\n            backgroundColor: \"#f8f8f6\"\n        },\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"w-full bg-luxury-green text-white py-4 px-6 shadow-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-6xl mx-auto flex justify-between items-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Check_Clock_Scissors_Shield_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"h-6 w-6 text-luxury-gold\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"font-bold text-xl\",\n                                    children: \"clipsnip\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                    lineNumber: 107,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                            lineNumber: 105,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                    className: \"hidden md:flex items-center gap-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#features\",\n                                            className: \"text-white/90 hover:text-luxury-gold transition-colors\",\n                                            children: \"Features\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#testimonials\",\n                                            className: \"text-white/90 hover:text-luxury-gold transition-colors\",\n                                            children: \"Testimonials\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                            lineNumber: 115,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                            href: \"#plans\",\n                                            className: \"text-white/90 hover:text-luxury-gold transition-colors\",\n                                            children: \"Plans\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                            lineNumber: 118,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                    href: \"/login\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                        variant: \"secondary\",\n                                        size: \"sm\",\n                                        className: \"bg-luxury-gold text-luxury-green hover:bg-luxury-gold-light font-medium\",\n                                        children: \"Login\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                        lineNumber: 124,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                            lineNumber: 110,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                    lineNumber: 104,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                lineNumber: 103,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"w-full py-20 md:py-32 px-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-6xl mx-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col lg:flex-row items-center gap-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full lg:w-1/2 text-center lg:text-left\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                        className: \"inline-flex items-center gap-2 bg-luxury-green/10 text-luxury-green px-4 py-2 rounded-full mb-6\",\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            duration: 0.5\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Check_Clock_Scissors_Shield_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"h-4 w-4 text-luxury-gold\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                lineNumber: 147,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm font-medium\",\n                                                children: \"Trusted by 50,000+ creators\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                lineNumber: 148,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.h1, {\n                                        className: \"text-4xl md:text-5xl lg:text-6xl font-bold mb-6 leading-tight\",\n                                        style: {\n                                            color: \"#121212\"\n                                        },\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            duration: 0.5,\n                                            delay: 0.1\n                                        },\n                                        children: [\n                                            \"Create Viral Clips with\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-luxury-green\",\n                                                children: \" Royal Precision\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                lineNumber: 159,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.p, {\n                                        className: \"text-xl mb-8 leading-relaxed\",\n                                        style: {\n                                            color: \"#121212\"\n                                        },\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            duration: 0.5,\n                                            delay: 0.2\n                                        },\n                                        children: \"Transform any video into a short-form masterpiece with automatic cropping, premium subtitles, and AI-powered editing.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                        className: \"flex flex-col sm:flex-row gap-4 items-center justify-center lg:justify-start\",\n                                        initial: {\n                                            opacity: 0,\n                                            y: 20\n                                        },\n                                        animate: {\n                                            opacity: 1,\n                                            y: 0\n                                        },\n                                        transition: {\n                                            duration: 0.5,\n                                            delay: 0.3\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                href: \"/signup\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    className: \"h-14 px-8 text-lg font-medium rounded-2xl bg-luxury-green hover:shadow-xl hover:scale-105 transition-all duration-300\",\n                                                    children: \"Start Creating Now\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                    lineNumber: 179,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                lineNumber: 178,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                href: \"#features\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: \"outline\",\n                                                    className: \"h-14 px-8 text-lg font-medium rounded-2xl border-2 border-luxury-green text-luxury-green hover:bg-luxury-green hover:text-white transition-all duration-300\",\n                                                    children: \"See How It Works\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                    lineNumber: 184,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                lineNumber: 183,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                        lineNumber: 172,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm mt-4\",\n                                        style: {\n                                            color: \"#121212\"\n                                        },\n                                        children: \"No credit card required. 3 free clips to start.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                        lineNumber: 192,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                lineNumber: 140,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full lg:w-1/2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute -inset-4 bg-luxury-gold/20 rounded-3xl blur-xl opacity-70\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"relative bg-white rounded-2xl shadow-2xl overflow-hidden border border-luxury-gold/20\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"aspect-[4/3] flex items-center justify-center\",\n                                                style: {\n                                                    backgroundColor: \"#f8f8f6\"\n                                                },\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"relative w-[280px] h-[500px] rounded-3xl overflow-hidden shadow-2xl border-8\",\n                                                    style: {\n                                                        backgroundColor: \"#121212\",\n                                                        borderColor: \"#121212\"\n                                                    },\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute top-0 left-0 right-0 h-6 rounded-t-xl\",\n                                                            style: {\n                                                                backgroundColor: \"#121212\"\n                                                            }\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                            lineNumber: 201,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"h-full w-full bg-luxury-green/20 flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-center text-white\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Check_Clock_Scissors_Shield_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                        className: \"h-12 w-12 mx-auto mb-4 text-luxury-gold\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 204,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-lg font-medium\",\n                                                                        children: \"Your viral clip\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 205,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm opacity-70\",\n                                                                        children: \"Ready to dominate\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 206,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                                lineNumber: 203,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                            lineNumber: 202,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"absolute bottom-6 left-0 right-0 px-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"backdrop-blur-sm text-white p-3 rounded-xl border border-luxury-gold/30\",\n                                                                style: {\n                                                                    backgroundColor: \"#121212\"\n                                                                },\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-sm font-medium\",\n                                                                        children: \"Premium subtitles\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 211,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-xs opacity-80\",\n                                                                        children: \"Crafted for engagement\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                                        lineNumber: 212,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                                lineNumber: 210,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                            lineNumber: 209,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                    lineNumber: 200,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                lineNumber: 199,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                        lineNumber: 139,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                    lineNumber: 138,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                lineNumber: 137,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"features\",\n                className: \"w-full py-20 px-4\",\n                style: {\n                    backgroundColor: \"#ffffff\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-6xl mx-auto text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl md:text-4xl font-bold mb-4 text-luxury-green\",\n                            children: \"Why Creators Choose ClipSnip\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                            lineNumber: 227,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl mb-16 max-w-3xl mx-auto\",\n                            style: {\n                                color: \"#121212\"\n                            },\n                            children: \"Experience the luxury of effortless content creation with our premium AI-powered tools.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                            lineNumber: 228,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-3 gap-8\",\n                            children: features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_11__.motion.div, {\n                                    className: \"bg-white rounded-2xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-luxury-gold/10\",\n                                    initial: {\n                                        opacity: 0,\n                                        y: 20\n                                    },\n                                    animate: {\n                                        opacity: 1,\n                                        y: 0\n                                    },\n                                    transition: {\n                                        duration: 0.5,\n                                        delay: index * 0.1\n                                    },\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-16 h-16 bg-luxury-green rounded-2xl flex items-center justify-center mx-auto mb-6 text-white\",\n                                            children: feature.icon\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                            lineNumber: 241,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-bold mb-4 text-luxury-green\",\n                                            children: feature.title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                            lineNumber: 244,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"leading-relaxed\",\n                                            style: {\n                                                color: \"#121212\"\n                                            },\n                                            children: feature.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                            lineNumber: 245,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                            lineNumber: 232,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                    lineNumber: 226,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                lineNumber: 225,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"testimonials\",\n                className: \"w-full py-20 px-4 bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-6xl mx-auto text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl md:text-4xl font-bold mb-4 text-luxury-green\",\n                            children: \"Trusted by Elite Creators\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                            lineNumber: 255,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl mb-16\",\n                            style: {\n                                color: \"#121212\"\n                            },\n                            children: \"Join thousands of successful content creators who've elevated their game\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                            lineNumber: 256,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-3 gap-8\",\n                            children: testimonials.map((testimonial, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_testimonial_card__WEBPACK_IMPORTED_MODULE_6__.TestimonialCard, {\n                                    testimonial: testimonial\n                                }, index, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                    lineNumber: 262,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                            lineNumber: 260,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                    lineNumber: 254,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                lineNumber: 253,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                id: \"plans\",\n                className: \"w-full py-20 px-4\",\n                style: {\n                    backgroundColor: \"#ffffff\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-6xl mx-auto text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl md:text-4xl font-bold mb-4 text-luxury-green\",\n                            children: \"Choose Your Plan\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                            lineNumber: 271,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl mb-16 max-w-3xl mx-auto\",\n                            style: {\n                                color: \"#121212\"\n                            },\n                            children: \"Select the perfect plan for your content creation empire.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                            lineNumber: 272,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-3 gap-8\",\n                            children: plans.map((plan)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"rounded-2xl shadow-lg overflow-hidden border-2 transition-all hover:shadow-xl hover:scale-105 duration-300\", selectedPlan === plan.name.toLowerCase() ? \"border-luxury-gold shadow-luxury-gold/20\" : \"border-transparent\", plan.popular && \"ring-2 ring-luxury-gold ring-opacity-50\"),\n                                    onClick: ()=>setSelectedPlan(plan.name.toLowerCase()),\n                                    children: [\n                                        plan.popular && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-0 right-0 bg-luxury-gold text-luxury-green px-4 py-1 text-sm font-bold rounded-bl-lg\",\n                                            children: \"Most Popular\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                            lineNumber: 290,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                            className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"pb-4\", plan.name === \"Clipper\" ? \"bg-luxury-green/10\" : \"\"),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                    className: \"text-2xl text-luxury-green\",\n                                                    children: [\n                                                        plan.name,\n                                                        plan.name === \"Clipper\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"ml-2 text-xs bg-luxury-gold text-luxury-green px-2 py-0.5 rounded-full font-bold\",\n                                                            children: \"Premium\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                            lineNumber: 303,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                    lineNumber: 300,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-baseline mt-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-4xl font-bold text-luxury-green\",\n                                                            children: plan.price\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                            lineNumber: 309,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        plan.period && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            style: {\n                                                                color: \"#121212\"\n                                                            },\n                                                            children: plan.period\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                            lineNumber: 310,\n                                                            columnNumber: 37\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                    lineNumber: 308,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardDescription, {\n                                                    style: {\n                                                        color: \"#121212\"\n                                                    },\n                                                    children: plan.description\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                    lineNumber: 312,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                            lineNumber: 294,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                            className: \"pt-4\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"space-y-3\",\n                                                children: plan.features.map((feature)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-start gap-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Check_Clock_Scissors_Shield_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-5 w-5 text-luxury-gold shrink-0 mt-0.5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                                lineNumber: 318,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                style: {\n                                                                    color: \"#121212\"\n                                                                },\n                                                                children: feature\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                                lineNumber: 319,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, feature, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                        lineNumber: 317,\n                                                        columnNumber: 23\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                lineNumber: 315,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                            lineNumber: 314,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardFooter, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                                href: \"/signup\",\n                                                className: \"w-full\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_5__.cn)(\"w-full rounded-xl font-medium transition-all duration-300\", plan.name === \"Free\" ? \"hover:shadow-lg hover:scale-105\" : \"bg-luxury-green hover:shadow-lg hover:scale-105\"),\n                                                    style: plan.name === \"Free\" ? {\n                                                        backgroundColor: \"#f8f8f6\",\n                                                        color: \"#121212\"\n                                                    } : {},\n                                                    children: plan.cta\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                    lineNumber: 326,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                lineNumber: 325,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                            lineNumber: 324,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, plan.name, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                    lineNumber: 278,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                            lineNumber: 276,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-16\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                href: \"/signup\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    className: \"h-14 px-8 text-lg font-medium rounded-2xl bg-luxury-green hover:shadow-xl hover:scale-105 transition-all duration-300\",\n                                    children: [\n                                        \"Start Your Journey \",\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Check_Clock_Scissors_Shield_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"ml-2 h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                            lineNumber: 346,\n                                            columnNumber: 36\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                    lineNumber: 345,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                lineNumber: 344,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                            lineNumber: 343,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                    lineNumber: 270,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                lineNumber: 269,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"w-full py-12 px-6 bg-luxury-green text-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-6xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-4 gap-8 mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2 mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Check_Clock_Scissors_Shield_Star_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                    className: \"h-6 w-6 text-luxury-gold\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                    lineNumber: 359,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"font-bold text-xl\",\n                                                    children: \"clipsnip\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                    lineNumber: 360,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                            lineNumber: 358,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-white/80 text-sm leading-relaxed\",\n                                            children: \"Premium AI-powered video editing. Create viral clips with royal precision.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                            lineNumber: 362,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                    lineNumber: 357,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-bold mb-4 text-luxury-gold\",\n                                            children: \"Product\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                            lineNumber: 368,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#features\",\n                                                        className: \"text-white/80 hover:text-luxury-gold transition-colors text-sm\",\n                                                        children: \"Features\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                        lineNumber: 371,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                    lineNumber: 370,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#plans\",\n                                                        className: \"text-white/80 hover:text-luxury-gold transition-colors text-sm\",\n                                                        children: \"Plans\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                        lineNumber: 376,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                    lineNumber: 375,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#testimonials\",\n                                                        className: \"text-white/80 hover:text-luxury-gold transition-colors text-sm\",\n                                                        children: \"Testimonials\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                        lineNumber: 381,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                    lineNumber: 380,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                            lineNumber: 369,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                    lineNumber: 367,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-bold mb-4 text-luxury-gold\",\n                                            children: \"Company\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                            lineNumber: 389,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"text-white/80 hover:text-luxury-gold transition-colors text-sm\",\n                                                        children: \"About\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                        lineNumber: 392,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                    lineNumber: 391,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"text-white/80 hover:text-luxury-gold transition-colors text-sm\",\n                                                        children: \"Blog\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                        lineNumber: 397,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                    lineNumber: 396,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"text-white/80 hover:text-luxury-gold transition-colors text-sm\",\n                                                        children: \"Careers\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                        lineNumber: 402,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                    lineNumber: 401,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                            lineNumber: 390,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                    lineNumber: 388,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-bold mb-4 text-luxury-gold\",\n                                            children: \"Support\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                            lineNumber: 410,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"text-white/80 hover:text-luxury-gold transition-colors text-sm\",\n                                                        children: \"Contact\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                        lineNumber: 413,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                    lineNumber: 412,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"text-white/80 hover:text-luxury-gold transition-colors text-sm\",\n                                                        children: \"Terms\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                        lineNumber: 418,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                    lineNumber: 417,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                        href: \"#\",\n                                                        className: \"text-white/80 hover:text-luxury-gold transition-colors text-sm\",\n                                                        children: \"Privacy\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                        lineNumber: 423,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                                    lineNumber: 422,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                            lineNumber: 411,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                    lineNumber: 409,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                            lineNumber: 356,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"pt-8 border-t border-white/20 flex flex-col md:flex-row justify-between items-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-white/60 text-sm mb-4 md:mb-0\",\n                                    children: [\n                                        \"\\xa9 \",\n                                        new Date().getFullYear(),\n                                        \" ClipSnip. All rights reserved.\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                    lineNumber: 432,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center gap-2 text-sm text-white/60\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Crafted with\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                            lineNumber: 436,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-luxury-gold\",\n                                            children: \"♔\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                            lineNumber: 437,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"for creators\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                            lineNumber: 438,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                                    lineNumber: 435,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                            lineNumber: 431,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                    lineNumber: 355,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n                lineNumber: 354,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Clipsnip_ai\\\\app\\\\page.tsx\",\n        lineNumber: 101,\n        columnNumber: 5\n    }, this);\n}\n_s(LandingPage, \"DzoslR4k2TpIBywZQz/PcUL7kh4=\");\n_c = LandingPage;\nvar _c;\n$RefreshReg$(_c, \"LandingPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/page.tsx\n"));

/***/ })

});