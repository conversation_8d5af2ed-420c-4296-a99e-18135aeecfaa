{"version": 3, "sources": ["../../../src/server/web/adapter.ts"], "names": ["NextRequestHint", "adapter", "NextRequest", "constructor", "params", "input", "init", "sourcePage", "page", "request", "PageSignatureError", "respondWith", "waitUntil", "headersGetter", "keys", "headers", "Array", "from", "get", "key", "undefined", "propagator", "fn", "tracer", "getTracer", "withPropagatedContext", "testApisIntercepted", "ensureTestApisIntercepted", "process", "env", "NEXT_PRIVATE_TEST_PROXY", "interceptTestApis", "wrapRequestHandler", "require", "ensureInstrumentationRegistered", "isEdgeRendering", "self", "__BUILD_MANIFEST", "url", "normalizeRscURL", "requestUrl", "NextURL", "nextConfig", "searchParams", "value", "getAll", "NEXT_QUERY_PARAM_PREFIX", "startsWith", "normalizedKey", "substring", "length", "delete", "val", "append", "buildId", "isNextDataRequest", "pathname", "requestHeaders", "fromNodeOutgoingHttpHeaders", "flightHeaders", "Map", "param", "FLIGHT_PARAMETERS", "toString", "toLowerCase", "set", "normalizeUrl", "__NEXT_NO_MIDDLEWARE_URL_NORMALIZE", "URL", "stripInternalSearchParams", "body", "geo", "ip", "method", "signal", "Object", "defineProperty", "enumerable", "globalThis", "__incrementalCache", "IncrementalCache", "appDir", "fetchCache", "minimalMode", "NODE_ENV", "fetchCacheKeyPrefix", "__NEXT_FETCH_CACHE_KEY_PREFIX", "dev", "requestProtocol", "getPrerenderManifest", "version", "routes", "dynamicRoutes", "notFoundRoutes", "preview", "getEdgePreviewProps", "event", "NextFetchEvent", "response", "cookiesFromResponse", "isMiddleware", "trace", "MiddlewareSpan", "execute", "spanName", "nextUrl", "attributes", "RequestAsyncStorageWrapper", "wrap", "requestAsyncStorage", "req", "renderOpts", "onUpdateCookies", "cookies", "previewProps", "handler", "Response", "TypeError", "rewrite", "rewriteUrl", "forceLocale", "host", "String", "relativizedRewrite", "relativizeURL", "__NEXT_EXTERNAL_MIDDLEWARE_REWRITE_RESOLVE", "match", "redirect", "redirectURL", "finalResponse", "NextResponse", "next", "middlewareOverrideHeaders", "overwrittenHeaders", "push", "join", "Promise", "all", "waitUntilSymbol", "fetchMetrics"], "mappings": ";;;;;;;;;;;;;;;IAsBaA,eAAe;eAAfA;;IA8DSC,OAAO;eAAPA;;;uBAlFa;uBACS;4BACb;yBACH;0BACC;+BACC;yBAEN;+BACkB;0BACV;kCACE;2BACM;yBACQ;4CACL;6CACP;wBACV;4BAEK;qCACK;AAE7B,MAAMD,wBAAwBE,oBAAW;IAI9CC,YAAYC,MAIX,CAAE;QACD,KAAK,CAACA,OAAOC,KAAK,EAAED,OAAOE,IAAI;QAC/B,IAAI,CAACC,UAAU,GAAGH,OAAOI,IAAI;IAC/B;IAEA,IAAIC,UAAU;QACZ,MAAM,IAAIC,yBAAkB,CAAC;YAAEF,MAAM,IAAI,CAACD,UAAU;QAAC;IACvD;IAEAI,cAAc;QACZ,MAAM,IAAID,yBAAkB,CAAC;YAAEF,MAAM,IAAI,CAACD,UAAU;QAAC;IACvD;IAEAK,YAAY;QACV,MAAM,IAAIF,yBAAkB,CAAC;YAAEF,MAAM,IAAI,CAACD,UAAU;QAAC;IACvD;AACF;AAEA,MAAMM,gBAAwC;IAC5CC,MAAM,CAACC,UAAYC,MAAMC,IAAI,CAACF,QAAQD,IAAI;IAC1CI,KAAK,CAACH,SAASI,MAAQJ,QAAQG,GAAG,CAACC,QAAQC;AAC7C;AASA,IAAIC,aAA8D,CAChEZ,SACAa;IAEA,MAAMC,SAASC,IAAAA,iBAAS;IACxB,OAAOD,OAAOE,qBAAqB,CAAChB,QAAQM,OAAO,EAAEO,IAAIT;AAC3D;AAEA,IAAIa,sBAAsB;AAE1B,SAASC;IACP,IAAI,CAACD,qBAAqB;QACxBA,sBAAsB;QACtB,IAAIE,QAAQC,GAAG,CAACC,uBAAuB,KAAK,QAAQ;YAClD,MAAM,EACJC,iBAAiB,EACjBC,kBAAkB,EACnB,GAAGC,QAAQ;YACZF;YACAV,aAAaW,mBAAmBX;QAClC;IACF;AACF;AAEO,eAAepB,QACpBG,MAAsB;IAEtBuB;IACA,MAAMO,IAAAA,wCAA+B;IAErC,yCAAyC;IACzC,MAAMC,kBAAkB,OAAOC,KAAKC,gBAAgB,KAAK;IAEzDjC,OAAOK,OAAO,CAAC6B,GAAG,GAAGC,IAAAA,yBAAe,EAACnC,OAAOK,OAAO,CAAC6B,GAAG;IAEvD,MAAME,aAAa,IAAIC,gBAAO,CAACrC,OAAOK,OAAO,CAAC6B,GAAG,EAAE;QACjDvB,SAASX,OAAOK,OAAO,CAACM,OAAO;QAC/B2B,YAAYtC,OAAOK,OAAO,CAACiC,UAAU;IACvC;IAEA,yIAAyI;IACzI,4CAA4C;IAC5C,MAAM5B,OAAO;WAAI0B,WAAWG,YAAY,CAAC7B,IAAI;KAAG;IAChD,KAAK,MAAMK,OAAOL,KAAM;QACtB,MAAM8B,QAAQJ,WAAWG,YAAY,CAACE,MAAM,CAAC1B;QAE7C,IACEA,QAAQ2B,kCAAuB,IAC/B3B,IAAI4B,UAAU,CAACD,kCAAuB,GACtC;YACA,MAAME,gBAAgB7B,IAAI8B,SAAS,CAACH,kCAAuB,CAACI,MAAM;YAClEV,WAAWG,YAAY,CAACQ,MAAM,CAACH;YAE/B,KAAK,MAAMI,OAAOR,MAAO;gBACvBJ,WAAWG,YAAY,CAACU,MAAM,CAACL,eAAeI;YAChD;YACAZ,WAAWG,YAAY,CAACQ,MAAM,CAAChC;QACjC;IACF;IAEA,4DAA4D;IAC5D,MAAMmC,UAAUd,WAAWc,OAAO;IAClCd,WAAWc,OAAO,GAAG;IAErB,MAAMC,oBAAoBnD,OAAOK,OAAO,CAACM,OAAO,CAAC,gBAAgB;IAEjE,IAAIwC,qBAAqBf,WAAWgB,QAAQ,KAAK,UAAU;QACzDhB,WAAWgB,QAAQ,GAAG;IACxB;IAEA,MAAMC,iBAAiBC,IAAAA,kCAA2B,EAACtD,OAAOK,OAAO,CAACM,OAAO;IACzE,MAAM4C,gBAAgB,IAAIC;IAC1B,oDAAoD;IACpD,IAAI,CAACzB,iBAAiB;QACpB,KAAK,MAAM0B,SAASC,mCAAiB,CAAE;YACrC,MAAM3C,MAAM0C,MAAME,QAAQ,GAAGC,WAAW;YACxC,MAAMpB,QAAQa,eAAevC,GAAG,CAACC;YACjC,IAAIyB,OAAO;gBACTe,cAAcM,GAAG,CAAC9C,KAAKsC,eAAevC,GAAG,CAACC;gBAC1CsC,eAAeN,MAAM,CAAChC;YACxB;QACF;IACF;IAEA,MAAM+C,eAAetC,QAAQC,GAAG,CAACsC,kCAAkC,GAC/D,IAAIC,IAAIhE,OAAOK,OAAO,CAAC6B,GAAG,IAC1BE;IAEJ,MAAM/B,UAAU,IAAIT,gBAAgB;QAClCQ,MAAMJ,OAAOI,IAAI;QACjB,mDAAmD;QACnDH,OAAOgE,IAAAA,wCAAyB,EAACH,cAAc,MAAMH,QAAQ;QAC7DzD,MAAM;YACJgE,MAAMlE,OAAOK,OAAO,CAAC6D,IAAI;YACzBC,KAAKnE,OAAOK,OAAO,CAAC8D,GAAG;YACvBxD,SAAS0C;YACTe,IAAIpE,OAAOK,OAAO,CAAC+D,EAAE;YACrBC,QAAQrE,OAAOK,OAAO,CAACgE,MAAM;YAC7B/B,YAAYtC,OAAOK,OAAO,CAACiC,UAAU;YACrCgC,QAAQtE,OAAOK,OAAO,CAACiE,MAAM;QAC/B;IACF;IAEA;;;;GAIC,GACD,IAAInB,mBAAmB;QACrBoB,OAAOC,cAAc,CAACnE,SAAS,YAAY;YACzCoE,YAAY;YACZjC,OAAO;QACT;IACF;IAEA,IACE,CAAC,AAACkC,WAAmBC,kBAAkB,IACvC,AAAC3E,OAAe4E,gBAAgB,EAChC;QACEF,WAAmBC,kBAAkB,GAAG,IAAI,AAC5C3E,OACA4E,gBAAgB,CAAC;YACjBC,QAAQ;YACRC,YAAY;YACZC,aAAavD,QAAQC,GAAG,CAACuD,QAAQ,KAAK;YACtCC,qBAAqBzD,QAAQC,GAAG,CAACyD,6BAA6B;YAC9DC,KAAK3D,QAAQC,GAAG,CAACuD,QAAQ,KAAK;YAC9B3B,gBAAgBrD,OAAOK,OAAO,CAACM,OAAO;YACtCyE,iBAAiB;YACjBC,sBAAsB;gBACpB,OAAO;oBACLC,SAAS,CAAC;oBACVC,QAAQ,CAAC;oBACTC,eAAe,CAAC;oBAChBC,gBAAgB,EAAE;oBAClBC,SAASC,IAAAA,wCAAmB;gBAC9B;YACF;QACF;IACF;IAEA,MAAMC,QAAQ,IAAIC,0BAAc,CAAC;QAAExF;QAASD,MAAMJ,OAAOI,IAAI;IAAC;IAC9D,IAAI0F;IACJ,IAAIC;IAEJD,WAAW,MAAM7E,WAAWZ,SAAS;QACnC,8DAA8D;QAC9D,MAAM2F,eACJhG,OAAOI,IAAI,KAAK,iBAAiBJ,OAAOI,IAAI,KAAK;QACnD,IAAI4F,cAAc;YAChB,OAAO5E,IAAAA,iBAAS,IAAG6E,KAAK,CACtBC,0BAAc,CAACC,OAAO,EACtB;gBACEC,UAAU,CAAC,WAAW,EAAE/F,QAAQgE,MAAM,CAAC,CAAC,EAAEhE,QAAQgG,OAAO,CAACjD,QAAQ,CAAC,CAAC;gBACpEkD,YAAY;oBACV,eAAejG,QAAQgG,OAAO,CAACjD,QAAQ;oBACvC,eAAe/C,QAAQgE,MAAM;gBAC/B;YACF,GACA,IACEkC,sDAA0B,CAACC,IAAI,CAC7BC,gDAAmB,EACnB;oBACEC,KAAKrG;oBACLsG,YAAY;wBACVC,iBAAiB,CAACC;4BAChBd,sBAAsBc;wBACxB;wBACA,2EAA2E;wBAC3EC,cAAcnB,IAAAA,wCAAmB;oBACnC;gBACF,GACA,IAAM3F,OAAO+G,OAAO,CAAC1G,SAASuF;QAGtC;QACA,OAAO5F,OAAO+G,OAAO,CAAC1G,SAASuF;IACjC;IAEA,yCAAyC;IACzC,IAAIE,YAAY,CAAEA,CAAAA,oBAAoBkB,QAAO,GAAI;QAC/C,MAAM,IAAIC,UAAU;IACtB;IAEA,IAAInB,YAAYC,qBAAqB;QACnCD,SAASnF,OAAO,CAACkD,GAAG,CAAC,cAAckC;IACrC;IAEA;;;;;GAKC,GACD,MAAMmB,UAAUpB,4BAAAA,SAAUnF,OAAO,CAACG,GAAG,CAAC;IACtC,IAAIgF,YAAYoB,WAAW,CAACnF,iBAAiB;QAC3C,MAAMoF,aAAa,IAAI9E,gBAAO,CAAC6E,SAAS;YACtCE,aAAa;YACbzG,SAASX,OAAOK,OAAO,CAACM,OAAO;YAC/B2B,YAAYtC,OAAOK,OAAO,CAACiC,UAAU;QACvC;QAEA,IAAI,CAACd,QAAQC,GAAG,CAACsC,kCAAkC,EAAE;YACnD,IAAIoD,WAAWE,IAAI,KAAKhH,QAAQgG,OAAO,CAACgB,IAAI,EAAE;gBAC5CF,WAAWjE,OAAO,GAAGA,WAAWiE,WAAWjE,OAAO;gBAClD4C,SAASnF,OAAO,CAACkD,GAAG,CAAC,wBAAwByD,OAAOH;YACtD;QACF;QAEA;;;;KAIC,GACD,MAAMI,qBAAqBC,IAAAA,4BAAa,EACtCF,OAAOH,aACPG,OAAOlF;QAGT,IACEe,qBACA,kDAAkD;QAClD,oDAAoD;QACpD,yCAAyC;QACzC,CACE3B,CAAAA,QAAQC,GAAG,CAACgG,0CAA0C,IACtDF,mBAAmBG,KAAK,CAAC,gBAAe,GAE1C;YACA5B,SAASnF,OAAO,CAACkD,GAAG,CAAC,oBAAoB0D;QAC3C;IACF;IAEA;;;;GAIC,GACD,MAAMI,WAAW7B,4BAAAA,SAAUnF,OAAO,CAACG,GAAG,CAAC;IACvC,IAAIgF,YAAY6B,YAAY,CAAC5F,iBAAiB;QAC5C,MAAM6F,cAAc,IAAIvF,gBAAO,CAACsF,UAAU;YACxCP,aAAa;YACbzG,SAASX,OAAOK,OAAO,CAACM,OAAO;YAC/B2B,YAAYtC,OAAOK,OAAO,CAACiC,UAAU;QACvC;QAEA;;;KAGC,GACDwD,WAAW,IAAIkB,SAASlB,SAAS5B,IAAI,EAAE4B;QAEvC,IAAI,CAACtE,QAAQC,GAAG,CAACsC,kCAAkC,EAAE;YACnD,IAAI6D,YAAYP,IAAI,KAAKhH,QAAQgG,OAAO,CAACgB,IAAI,EAAE;gBAC7CO,YAAY1E,OAAO,GAAGA,WAAW0E,YAAY1E,OAAO;gBACpD4C,SAASnF,OAAO,CAACkD,GAAG,CAAC,YAAYyD,OAAOM;YAC1C;QACF;QAEA;;;;KAIC,GACD,IAAIzE,mBAAmB;YACrB2C,SAASnF,OAAO,CAACoC,MAAM,CAAC;YACxB+C,SAASnF,OAAO,CAACkD,GAAG,CAClB,qBACA2D,IAAAA,4BAAa,EAACF,OAAOM,cAAcN,OAAOlF;QAE9C;IACF;IAEA,MAAMyF,gBAAgB/B,WAAWA,WAAWgC,sBAAY,CAACC,IAAI;IAE7D,iFAAiF;IACjF,MAAMC,4BAA4BH,cAAclH,OAAO,CAACG,GAAG,CACzD;IAEF,MAAMmH,qBAA+B,EAAE;IACvC,IAAID,2BAA2B;QAC7B,KAAK,MAAM,CAACjH,KAAKyB,MAAM,IAAIe,cAAe;YACxCsE,cAAclH,OAAO,CAACkD,GAAG,CAAC,CAAC,qBAAqB,EAAE9C,IAAI,CAAC,EAAEyB;YACzDyF,mBAAmBC,IAAI,CAACnH;QAC1B;QAEA,IAAIkH,mBAAmBnF,MAAM,GAAG,GAAG;YACjC+E,cAAclH,OAAO,CAACkD,GAAG,CACvB,iCACAmE,4BAA4B,MAAMC,mBAAmBE,IAAI,CAAC;QAE9D;IACF;IAEA,OAAO;QACLrC,UAAU+B;QACVrH,WAAW4H,QAAQC,GAAG,CAACzC,KAAK,CAAC0C,2BAAe,CAAC;QAC7CC,cAAclI,QAAQkI,YAAY;IACpC;AACF"}