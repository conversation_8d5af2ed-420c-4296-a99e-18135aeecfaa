const express = require('express');
const multer = require('multer');
const cors = require('cors');
const path = require('path');
const fs = require('fs');
const ffmpeg = require('fluent-ffmpeg');
const { v4: uuidv4 } = require('uuid');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 5000;

// Middleware
app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// Create uploads directory if it doesn't exist
const uploadsDir = './uploads';
const outputDir = './output';
if (!fs.existsSync(uploadsDir)) fs.mkdirSync(uploadsDir);
if (!fs.existsSync(outputDir)) fs.mkdirSync(outputDir);

// Configure multer for file uploads
const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    cb(null, uploadsDir);
  },
  filename: (req, file, cb) => {
    const uniqueName = `${uuidv4()}-${file.originalname}`;
    cb(null, uniqueName);
  }
});

const upload = multer({
  storage,
  limits: { fileSize: 100 * 1024 * 1024 }, // 100MB limit
  fileFilter: (req, file, cb) => {
    const allowedTypes = /mp4|avi|mov|wmv|flv|webm|mkv/;
    const extname = allowedTypes.test(path.extname(file.originalname).toLowerCase());
    const mimetype = allowedTypes.test(file.mimetype);
    
    if (mimetype && extname) {
      return cb(null, true);
    } else {
      cb(new Error('Only video files are allowed!'));
    }
  }
});

// Routes
app.get('/', (req, res) => {
  res.json({ message: 'Clipsnip API is running!' });
});

// Upload video endpoint
app.post('/api/upload', upload.single('video'), (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No video file uploaded' });
    }

    const videoInfo = {
      id: path.parse(req.file.filename).name,
      originalName: req.file.originalname,
      filename: req.file.filename,
      size: req.file.size,
      path: req.file.path
    };

    res.json({
      message: 'Video uploaded successfully',
      video: videoInfo
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Get video info endpoint
app.get('/api/video/:id/info', (req, res) => {
  const videoId = req.params.id;
  const videoPath = path.join(uploadsDir, `${videoId}.mp4`);

  if (!fs.existsSync(videoPath)) {
    return res.status(404).json({ error: 'Video not found' });
  }

  ffmpeg.ffprobe(videoPath, (err, metadata) => {
    if (err) {
      return res.status(500).json({ error: 'Failed to get video info' });
    }

    const videoStream = metadata.streams.find(stream => stream.codec_type === 'video');
    
    res.json({
      duration: metadata.format.duration,
      width: videoStream.width,
      height: videoStream.height,
      fps: eval(videoStream.r_frame_rate),
      format: metadata.format.format_name
    });
  });
});

// Clip video endpoint
app.post('/api/video/:id/clip', (req, res) => {
  const videoId = req.params.id;
  const { startTime, endTime, outputName } = req.body;
  
  const inputPath = path.join(uploadsDir, `${videoId}.mp4`);
  const outputFilename = `${outputName || uuidv4()}.mp4`;
  const outputPath = path.join(outputDir, outputFilename);

  if (!fs.existsSync(inputPath)) {
    return res.status(404).json({ error: 'Video not found' });
  }

  ffmpeg(inputPath)
    .setStartTime(startTime)
    .setDuration(endTime - startTime)
    .output(outputPath)
    .on('end', () => {
      res.json({
        message: 'Video clipped successfully',
        outputFile: outputFilename,
        downloadUrl: `/api/download/${outputFilename}`
      });
    })
    .on('error', (err) => {
      res.status(500).json({ error: 'Failed to clip video: ' + err.message });
    })
    .run();
});

// Crop video endpoint
app.post('/api/video/:id/crop', (req, res) => {
  const videoId = req.params.id;
  const { x, y, width, height, outputName } = req.body;
  
  const inputPath = path.join(uploadsDir, `${videoId}.mp4`);
  const outputFilename = `${outputName || uuidv4()}.mp4`;
  const outputPath = path.join(outputDir, outputFilename);

  if (!fs.existsSync(inputPath)) {
    return res.status(404).json({ error: 'Video not found' });
  }

  ffmpeg(inputPath)
    .videoFilters(`crop=${width}:${height}:${x}:${y}`)
    .output(outputPath)
    .on('end', () => {
      res.json({
        message: 'Video cropped successfully',
        outputFile: outputFilename,
        downloadUrl: `/api/download/${outputFilename}`
      });
    })
    .on('error', (err) => {
      res.status(500).json({ error: 'Failed to crop video: ' + err.message });
    })
    .run();
});

// Download processed video
app.get('/api/download/:filename', (req, res) => {
  const filename = req.params.filename;
  const filePath = path.join(outputDir, filename);

  if (!fs.existsSync(filePath)) {
    return res.status(404).json({ error: 'File not found' });
  }

  res.download(filePath);
});

// Error handling middleware
app.use((error, req, res, next) => {
  if (error instanceof multer.MulterError) {
    if (error.code === 'LIMIT_FILE_SIZE') {
      return res.status(400).json({ error: 'File too large' });
    }
  }
  res.status(500).json({ error: error.message });
});

app.listen(PORT, () => {
  console.log(`Clipsnip server running on port ${PORT}`);
});
